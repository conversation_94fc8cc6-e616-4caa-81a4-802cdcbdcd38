package fpi.upck.banks.common.engine.net;

import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;
import fpi.upck.utils.Utils;
import org.apache.hc.client5.http.async.methods.*;
import org.apache.hc.client5.http.classic.methods.*;
import org.apache.hc.client5.http.entity.UrlEncodedFormEntity;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.client5.http.impl.async.CloseableHttpAsyncClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.concurrent.FutureCallback;
import org.apache.hc.core5.http.*;
import org.apache.hc.core5.http.io.entity.ByteArrayEntity;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.http.message.BasicNameValuePair;
import org.apache.hc.core5.http.nio.AsyncEntityProducer;
import org.apache.hc.core5.http.nio.AsyncRequestProducer;
import org.apache.hc.core5.http.nio.AsyncResponseConsumer;
import org.apache.hc.core5.http.nio.entity.BasicAsyncEntityProducer;
import org.apache.hc.core5.http.nio.support.BasicRequestProducer;

import javax.net.ssl.SSLException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class UpbHttpClient5 extends UpbHttpEngine {

    public UpbHttpClient5(UPN upn) {
        super(upn);
    }


    @Override
    protected <T> UPN.UpnResponse<T> doExecute(UPN.Options options) {
        CloseableHttpResponse httpResponse = this.requestByHttpClient5(options);
        var response = this.<T>transformResponse(httpResponse, options);
        /**
         * 精确判断一些通用错误,直接外抛
         */
        this.throwGenericResponse(response);
        return response;
    }


    /**
     * 解析一些回包的通用错误
     *
     * @return
     */
    public <T, D extends UPN.UpnResponse<T>> void throwGenericResponse(D resp) throws UpckException {
        if (resp.code == 502 && !Utils.isEmpty(resp.message)) {
            if (resp.message.startsWith("Proxy Error:")) {
                //Response code=502,msg=Proxy Error: server_error Error creating ext request: TUN_ERR: Destination host connect timeout,payload is empty
                throw UpckErrCode.ERR_NET_PROXY_ERROR_502();
            }
        }
        if (resp.code == 407) {
            //code 407,message=Invalid Auth,EmptyBody
            throw UpckErrCode.ERR_NET_PROXY_407();
        }
    }


    @Override
    protected <T> CompletableFuture<UPN.UpnResponse<T>> doPost(UPN.Options options) {
        CloseableHttpAsyncClient client = this.upn.getApacheHttpAsyncClient();
        var future = new CompletableFuture<UPN.UpnResponse<T>>();
        var that = this;
        /*
        创建一个异步的producer
         */
        AsyncRequestProducer producer = this.createRequestAsync(options);
        /*
        创建一个通用的处理response数据的方法
         */
        AsyncResponseConsumer<SimpleHttpResponse> consumer = SimpleResponseConsumer.create();
        client.execute(producer, consumer,
                new FutureCallback<>() {
                    @Override
                    public void completed(SimpleHttpResponse httpResponse) {
                        try {
                            var response = that.<T>transformSimpleResponse(httpResponse, options);
                            that.throwGenericResponse(response);
                            future.complete(response);
                        } catch (Exception e) {
                            future.completeExceptionally(e);
                        }
                    }

                    @Override
                    public void failed(Exception ex) {
                        future.completeExceptionally(that.convertNetworkException(ex));
                    }

                    @Override
                    public void cancelled() {
                        future.cancel(true);
                    }
                }
        );
        return future;
    }

    public <T> UPN.UpnResponse<T> transformSimpleResponse(
            SimpleHttpResponse aphResp,
            UPN.Options options) throws Exception {
        UPN.UpnResponse<T> resp = new UPN.UpnResponse<>();
        resp.code = aphResp.getCode();
        resp.message = aphResp.getReasonPhrase();
        /**
         * 解析 headers
         */
        Header[] headers = aphResp.getHeaders();
        resp.headers = new HashMap<>();
        Arrays.stream(headers).forEach(header -> {
            String key = header.getName();
            List<String> values = resp.headers.computeIfAbsent(key, k -> new ArrayList<>());
            values.add(header.getValue());
        });

        //读取数据

        do {
            resp.rawBuf = aphResp.getBody().getBodyBytes();
            aphResp.clear();
        } while (false);
        if (!byte[].class.equals(options.respType)) {
            resp.rawBody = new String(resp.rawBuf);
        }
        return resp;
    }


    public CloseableHttpResponse requestByHttpClient5(UPN.Options options) {
        HttpUriRequestBase request = this.createRequest(options);
        try {
            return this.upn.getApacheHttpClient()
                    .execute(request);
        } catch (Exception e) {
            throw this.convertNetworkException(e);
        }
    }




    public UpckException convertNetworkException(Exception e) {
        if (e instanceof SSLException) {
            if (e.getMessage().startsWith("Unsupported or unrecognized SSL message")) {
                return UpckErrCode.ERR_NET_SSL_UNSUPPORTED();
            }
        } else if (e instanceof ConnectionClosedException) {
            if (e.getMessage().startsWith("Connection closed by peer")) {
                return UpckErrCode.ERR_NET_CONNECTION_CLOSED_PEER();
            }
        }
        return new UpckException(e);
    }


    private HttpUriRequestBase createRequest(UPN.Options options) {
        HttpUriRequestBase request;
        String url = options.formatUrl();

        if (options.method() == UPN.Method.GET) {
            request = new HttpGet(url);
        } else if (options.method() == UPN.Method.POST) {
            request = new HttpPost(url);
        } else if (options.method() == UPN.Method.DELETE) {
            request = new HttpDelete(url);
        } else {
            request = new HttpPatch(url);
        }
        /**
         * 塞入headers
         */
        Map<String, String> headers = options.headers();
        if (headers != null) {
            Set<String> keys = headers.keySet();
            for (String key : keys) {
                String value = headers.get(key);
                if (key == null || value == null) {
                    throw new UpckException("Http Header Key or Value is Null", UpckErrCode.DEV_FATAL);
                }
                request.addHeader(key, value);
            }
        }

        if (options.reqBodyType() == UPN.ReqBodyType.FORM_MULTI) {
            request.removeHeaders("Content-Type");
        }
        /**
         * 塞入body
         */
        request.setEntity(this.transformEntity(options));
        return request;
    }

    private AsyncRequestProducer createRequestAsync(UPN.Options options) {
        String url = options.formatUrl();
        SimpleRequestBuilder builder = switch (options.method()) {
            case GET -> SimpleRequestBuilder.create(Method.GET);
            case POST -> SimpleRequestBuilder.create(Method.POST);
            case PATCH -> SimpleRequestBuilder.create(Method.PATCH);
            case DELETE -> SimpleRequestBuilder.create(Method.DELETE);
            case PUT -> SimpleRequestBuilder.create(Method.PUT);
        };
        builder.setUri(url);

        /**
         * 塞入headers
         */
        Map<String, String> headers = options.headers();
        if (headers != null) {
            Set<String> keys = headers.keySet();
            for (String key : keys) {
                String value = headers.get(key);
                if (key == null || value == null) {
                    throw new UpckException("Http Header Key or Value is Null", UpckErrCode.DEV_FATAL);
                }
                builder.addHeader(key, value);
            }
        }
        if (options.reqBodyType() == UPN.ReqBodyType.FORM_MULTI) {
            builder.removeHeaders("Content-Type");
        }
        HttpEntity entity = this.transformEntity(options);
        AsyncEntityProducer producer = this.castEntityToProducer(entity);
        return new BasicRequestProducer(builder.build(), producer);
    }

    public AsyncEntityProducer castEntityToProducer(HttpEntity httpEntity) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            httpEntity.writeTo(baos);
        } catch (IOException e) {
            throw new UpckException(e);
        }
        return new BasicAsyncEntityProducer(baos.toByteArray(), ContentType.parse(httpEntity.getContentType()));
    }


    public <T> UPN.UpnResponse<T> transformResponse(
            CloseableHttpResponse aphResp,
            UPN.Options options) {
        UPN.UpnResponse<T> resp = new UPN.UpnResponse<>();
        resp.code = aphResp.getCode();
        resp.message = aphResp.getReasonPhrase();
        /**
         * 解析 headers
         */
        Header[] headers = aphResp.getHeaders();
        resp.headers = new HashMap<>();
        Arrays.stream(headers).forEach(header -> {
            String key = header.getName();
            List<String> values = resp.headers.computeIfAbsent(key, k -> new ArrayList<>());
            values.add(header.getValue());
        });

        //读取数据
        HttpEntity respEntity = aphResp.getEntity();
        try {
            do {
                if (respEntity == null) {
                    aphResp.close();
                    break;
                }
                InputStream is = respEntity.getContent();
                if (is == null) {
                    aphResp.close();
                    break;
                }
                resp.rawBuf = is.readAllBytes();
                aphResp.close();
            } while (false);
        } catch (Exception e) {
            throw new UpckException(e);
        }

        if (!byte[].class.equals(options.respType)) {
            resp.rawBody = new String(resp.rawBuf);
        }
        return resp;
    }

    public HttpEntity transformEntity(UPN.Options options) {
        return new ByteArrayEntity(options.contentBytes(), ContentType.parse(options.mediaType()));
    }


    public HttpEntity transformEntity2(UPN.Options options) {
        UPN.ReqBodyType bodyType = options.reqBodyType();
        Object body = options.body();

        if (bodyType == UPN.ReqBodyType.BUFFER)
            return null;

        /**
         * form表单
         */
        if (bodyType == UPN.ReqBodyType.FORM) {
            Map<String, String> inputMap = new HashMap<>();
            if (body != null) inputMap = (Map<String, String>) body;
            return this.bodyToFormEntity(inputMap);
        } else if (bodyType == UPN.ReqBodyType.FORM_MULTI) {
            Map<String, String> inputMap = new HashMap<>();
            if (body != null) inputMap = (Map<String, String>) body;
            return this.bodyToMultipleFormEntity(inputMap);
        }


        String content;
        ContentType contentType;
        if (bodyType == UPN.ReqBodyType.JSON) {
            content = this.bodyToJson(body);
            contentType = ContentType.APPLICATION_JSON;
        } else {
            content = (String) body;
            content = Utils.isEmpty(content) ? "" : content;
            contentType = ContentType.TEXT_PLAIN;
        }
        return new StringEntity(content, contentType);
    }

    public UrlEncodedFormEntity bodyToFormEntity(Map<String, String> input) {
        // 表单参数
        List<NameValuePair> parameters = input.keySet().stream()
                .map(key -> new BasicNameValuePair(key, input.get(key)))
                .collect(Collectors.toList());
        return new UrlEncodedFormEntity(parameters);
    }


    public HttpEntity bodyToMultipleFormEntity(Map<String, String> input) {
        /**
         * 这里临时先这样写,未来有上传的方法,再细化
         */
        MultipartEntityBuilder builder = MultipartEntityBuilder.create()
                .setCharset(StandardCharsets.UTF_8)
                .setContentType(ContentType.MULTIPART_FORM_DATA);
        input.keySet().forEach(k -> builder.addTextBody(k, input.get(k),
                ContentType.create("text/plain", StandardCharsets.UTF_8))
        );
        return builder.build();

    }


}
