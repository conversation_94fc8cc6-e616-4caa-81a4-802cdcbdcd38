package fpi.upck.banks.in.paytm.models.dvbd;

import fpi.upck.utils.Utils;

/**
 * 设备绑定变量类
 * 用于配置不同的设备绑定流程参数
 */
public class DbdVars {
    /**
     * 流程类型，支持以下值：
     * - "MULTI_PSP_SILENT": 多PSP静默模式，包含完整参数集
     * - "multi_psp_deb": 多PSP调试模式，包含pspId但不含locTime、lang_id
     * - "LOGIN_REGISTER": 登录注册模式，基础参数集
     */
    public String flow;

    /**
     * 流程名称，用于标识具体的业务流程
     */
    public String flowName;

    /**
     * 子原因，用于详细描述流程触发的原因
     */
    public String subReason = "";

    /**
     * PSP ID，支付服务提供商标识
     * - "ptyes": Yes Bank
     * - "ptsbi": SBI Bank
     * - "pthdfc": HDFC Bank
     */
    public String pspId;

    /**
     * 初始化原因，用于设备绑定初始化接口
     * - "": 空字符串（silent模式）
     * - "oauth_deb_failed": OAuth设备绑定失败（login模式）
     */
    public String initReason;

    /**
     * 垂直业务名称，用于标识业务类型
     * - "TPAP_UPI": UPI支付业务（check_balance模式）
     * - "OAUTH_H5": OAuth H5业务（silent_deb模式）
     * - "AUTH": 认证业务（login模式）
     */
    public String verticalName;



    /**
     * 验证方法，用于login模式
     * - "otp": OTP验证
     * - "sms": SMS验证
     */
    public String method;

    /**
     * 完整性组ID，用于login模式的安全验证（内部随机生成，不作为外部参数）
     */
    public String integrityGroupId;


    public boolean loginRequest;

    /**
     * 创建MULTI_PSP_SILENT模式的变量
     *
     * @param pspId PSP标识，如"ptyes"、"pthdfc"
     * @return DevicebindingVars实例
     */
    public static DbdVars createMultiPspSilent(String pspId) {
        DbdVars vars = new DbdVars();
        vars.flow = "MULTI_PSP_SILENT";
        vars.flowName = "silent";
        vars.subReason = "silent_deb";
        vars.pspId = Utils.or(pspId, "ptyes");
        vars.initReason = "silent_deb";
        vars.verticalName = "OAUTH_H5";
        return vars;
    }

    /**
     * 创建multi_psp_deb模式的变量
     *
     * @return DevicebindingVars实例
     */
    public static DbdVars createMultiPspDeb() {
        DbdVars vars = new DbdVars();
        vars.flow = "multi_psp_deb";
        vars.flowName = "deb";
        vars.subReason = "multi_psp_deb";
        vars.pspId = "ptsbi";
        vars.initReason = "";
        vars.verticalName = "OAUTH_H5";
        return vars;
    }


    /**
     * 创建check_balance模式的变量（用于init接口）
     *
     * @return DevicebindingVars实例
     */
    public static DbdVars createCheckBalance() {
        DbdVars vars = new DbdVars();
        vars.flow = "check_balance";
        vars.flowName = "check_balance";
        vars.subReason = "";
        vars.initReason = "";
        vars.verticalName = "TPAP_UPI";
        return vars;
    }


}
