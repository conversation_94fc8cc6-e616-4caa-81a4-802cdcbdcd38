package fpi.upck.banks.in.paytm;

import fpi.upck.banks.UpckBankerImpl;
import fpi.upck.banks.common.engine.net.UpbBankerNet;
import fpi.upck.banks.common.engine.risk.VDT;
import fpi.upck.banks.in.paytm.apis.PtmApiAccount;
import fpi.upck.banks.in.paytm.apis.PtmApiUpi;
import fpi.upck.banks.in.paytm.entity.DbdInitSv1Resp;
import fpi.upck.banks.in.paytm.entity.OtpStatusResp;
import fpi.upck.banks.in.paytm.entity.V2UserSv1Resp;
import fpi.upck.banks.in.paytm.entity.upi.resp.PaymentHistoryResponse;
import fpi.upck.banks.in.paytm.models.PtmFetchStrategy;
import fpi.upck.banks.in.paytm.models.dvbd.DbdConfigSv1Resp;
import fpi.upck.banks.in.paytm.models.dvbd.DbdVars;
import fpi.upck.banks.in.paytm.models.txn.PtmHistParams;
import fpi.upck.common.entity.FICK;
import fpi.upck.common.entity.auth.INPUT;
import fpi.upck.common.entity.bank.UpckBankType;
import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;
import fpi.upck.common.entity.payment.UpckPayOptions;
import fpi.upck.common.entity.payment.UpckPayToTask;
import fpi.upck.common.entity.psp.VpaDetails;
import fpi.upck.common.entity.transactions.QueryTxnItem;
import fpi.upck.common.entity.transactions.UpckQueryOptions;
import fpi.upck.utils.Utils;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class PtmBanker extends UpckBankerImpl {
    //    private PtmApiFactory apiFactory = new PtmApiFactory(this);
    private PtmApiAccount apiAccount = new PtmApiAccount(this);
    private PtmApiUpi upiAccount = new PtmApiUpi(this);

    public PtmBanker(InternalCallback appContext, String accountId) {
        super(appContext, accountId);
        this.deviceUtils = new PtmDeviceUtils(this);
        this.runtimeParams = new PTM.RT(this);
    }

    @Override
    public UpckBankType bankType() {
        return UpckBankType.PAYTM;
    }

    @Override
    public BigDecimal runQueryBalance(String cardId, String password, String vpa) {
        return null;
    }

    @Override
    public boolean runVpaCheckName(String vpa) {
        return false;
    }

    @Override
    protected List<UpbBankerNet> listOfChannels() {
        return List.of(this.upiAccount);
    }

    @Override
    protected List<String> listOfBaseUrls() {
        return List.of();
    }


    public PtmDeviceUtils getDeviceUtils() {
        return (PtmDeviceUtils) super.getDeviceUtils();
    }

    public void testSome() {
        var du = this.getDeviceUtils();
        var sadsf = this.upiAccount.fetchUserProfile();
        
        V2UserSv1Resp sdf = this.apiAccount.fetchV2UserSv1(
                PtmFetchStrategy.USERID,
                PtmFetchStrategy.USER_TYPE,
                PtmFetchStrategy.USER_ATTRIBUTE,
                PtmFetchStrategy.password_status,
                PtmFetchStrategy.kyc_state,
                PtmFetchStrategy.enc_user_id,
                PtmFetchStrategy.psp_deb_info,
                PtmFetchStrategy.external_user_id_b62,
                PtmFetchStrategy.DEFAULT
        );

        int xxx = 4;

    }


    public PTM.AUTH.LOGIN getAuthLoginData() {
        return (PTM.AUTH.LOGIN) super.getAuthLoginData();
    }


    /**
     * 设备绑定配置接口 - LOGIN_REGISTER模式
     * 用于登录注册流程的设备绑定配置
     *
     * @return DbdConfigSv1Resp 接口响应数据
     */
    public DbdConfigSv1Resp.Strategy fetchLoginDbdConfigSv1() {
        DbdVars vars = new DbdVars();
        vars.flow = "LOGIN_REGISTER";
        vars.flowName = "login";
        vars.subReason = "not_logged_in";
        vars.initReason = "oauth_deb_failed";
        vars.verticalName = "AUTH";
        vars.loginRequest = true;
        var respBody = this.apiAccount.fetchDbdConfigSv1(vars);
        DbdConfigSv1Resp.Strategy strategy = respBody.data.strategies.stream()
                .filter(s -> "login".equals(s.flow)) // 修正：使用正确的flow值
                .findAny().orElse(null);
        if (strategy == null) throw new UpckException("Strategy Unexpected", UpckErrCode.DEV_UNEXPECT);
        int methodSize = strategy.methods.size();
        if (methodSize == 0) throw new UpckException("Strategy Method Empty", UpckErrCode.DEV_UNEXPECT);
        if (methodSize > 1) {
            /*
            登录需要多种方法...目前预计不到
             */
            String methodNames = strategy.methods.stream()
                    .map(m -> m.name).collect(Collectors.joining("/"));
            throw new UpckException("Strategy Method Unexpected:" + methodNames, UpckErrCode.DEV_UNEXPECT);
        }
        return strategy;
    }

    /**
     * 设备绑定初始化接口 - login模式
     * 用于登录流程的设备绑定初始化（包含完整请求体）
     *
     * @return DbdInitSv1Resp 接口响应数据
     */
    public DbdInitSv1Resp fetchLoginDbdInitSv1() {
        DbdVars vars = new DbdVars();
        vars.flow = "login";
        vars.flowName = "login";
        vars.subReason = "token_not_found_in_cache";
        vars.initReason = "oauth_deb_failed";
        vars.verticalName = "AUTH";
        vars.method = "otp";
        vars.loginRequest = true;
        return this.apiAccount.fetchDbdInitSv1(vars);
    }

    @Override
    public FICK.SESSION.LOGIN onLoginPrepare(INPUT.LOGIN.PREPARE input, FICK.SESSION.LOGIN previous) {
        /*
        sv1 config,取登录信息
         */
        var configStrategy = this.fetchLoginDbdConfigSv1();
        this.logI("config strategy:" + Utils.gson.toJson(configStrategy));
        /*
        sv1 init,初始化登录信息
         */
        var dbdInit = this.fetchLoginDbdInitSv1();
        var initData = dbdInit.data;
        PTM.SESSION.LOGIN session = new PTM.SESSION.LOGIN();
        session.sessionId = initData.sessionId;
        return session;
    }

    @Override
    public FICK.AUTH.LOGIN onLoginConfirm(INPUT.LOGIN.CONFIRM input, FICK.SESSION.LOGIN loginSession) {
        PTM.SESSION.LOGIN ptmSession = (PTM.SESSION.LOGIN) loginSession;
        String otpCode = input.otpCode;
        String sessionId = ptmSession.sessionId;
        this.logI("Begin to Trigger Otp...");
        OtpStatusResp otpStatus = this.apiAccount.fetchOtpV1Status(sessionId, true);
        var respConfirm = this.apiAccount.fetchLoginDbdConfirmSv1(otpCode, sessionId, true);

        var respStatus = this.apiAccount.fetchDbdV2StatusSv1(sessionId, null, true);

        /*
        oauth2的操作
         */
        var respOauthInit = this.apiAccount.fetchOauth2AuthorizeInitSv1(sessionId);
        var initDate = respOauthInit.data;
        var valueType = respOauthInit.data.authenticationValueType;
        var nextSessionId = initDate.stateToken;
        var respOauthAuth = this.apiAccount.fetchOauth2V2AuthorizeSv1(valueType, nextSessionId);
        var code = respOauthAuth.data.code;//这个值一看就知道重要
        var respToken = this.apiAccount.fetchOauth2V3TokenSv1(code);

        PTM.AUTH.LOGIN loginData = new PTM.AUTH.LOGIN();
        loginData.oauthToken = respToken;
        return loginData;
    }

    @Override
    public FICK.SESSION.KYC onKycPrepare(INPUT.KYC.PREPARE prepareInput, FICK.SESSION.KYC previous) {
        return null;
    }

    @Override
    public FICK.AUTH.KYC onKycConfirm(INPUT.KYC.CONFIRM confirmInput, FICK.SESSION.KYC session, List<String> targetOperators) {
        return null;
    }

    @Override
    public FICK.SESSION.PAY onPayPrepare(List<UpckPayToTask> payTos, UpckPayOptions payOptions) {
        return null;
    }

    @Override
    public void onPayConfirm(List<UpckPayToTask> payTos, UpckPayOptions payOptions, FICK.SESSION.PAY session) {

    }

    @Override
    public <T extends FICK.DEVICE> Class<T> classDeviceStorage() {
        return (Class<T>) PTM.DEVICE.class;
    }

    @Override
    public <T extends FICK.AUTH.LOGIN> Class<T> classLoginAuth() {
        return (Class<T>) PTM.AUTH.LOGIN.class;
    }

    @Override
    public <T extends FICK.SESSION.LOGIN> Class<T> classLoginSession() {
        return (Class<T>) PTM.SESSION.LOGIN.class;
    }

    @Override
    public <T extends FICK.AUTH.KYC> Class<T> classKycAuth() {
        return (Class<T>) PTM.AUTH.KYC.class;
    }

    @Override
    public <T extends FICK.SESSION.KYC> Class<T> classKycSession() {
        return (Class<T>) PTM.SESSION.KYC.class;

    }

    @Override
    public <T extends FICK.SESSION.PAY> Class<T> classPaySession() {
        return (Class<T>) PTM.SESSION.PAY.class;

    }

    @Override
    public <T extends FICK.SESSION.PIN> Class<T> classPinSession() {
        return (Class<T>) PTM.SESSION.PIN.class;

    }

    @Override
    public <T, D extends FICK.ACCOUNT.UPI<T>> Class<D> classUpiDetails() {
        return (Class<D>) PTM.ACCOUNT.UPI.class;
    }

    @Override
    public <T, D extends FICK.ACCOUNT.CARD<T>> Class<D> classCard() {
        return (Class<D>) PTM.ACCOUNT.CARD.class;
    }

    @Override
    public <T, D extends FICK.BANK<T>> Class<D> classBank() {
        return (Class<D>) PTM.BANK.class;

    }

    @Override
    protected <T, F extends FICK.ACCOUNT.CARD<T>, D extends List<F>> D onConvertCardList(Object respCards) {
        return null;
    }

    @Override
    protected <T, D extends FICK.ACCOUNT.UPI<T>> D onConvertUpiDetails(Object respData) {
        return null;
    }

    @Override
    protected Object onRequestFetchCards() {
        return null;
    }

    @Override
    protected boolean onCardLinkToVpa(String cardId, String selfVpa) {
        return false;
    }

    @Override
    protected Object onRequestUpiDetails() {
        return null;
    }

    @Override
    protected <T, D extends FICK.BANK<T>> List<D> onQueryBankList() {
        return List.of();
    }

    @Override
    protected boolean onCardAdd(FICK.BANK bankInfo, String selfVpa) {
        return false;
    }

    @Override
    protected boolean onCardDelete(FICK.ACCOUNT.CARD card, String selfVpa) {
        return false;
    }

    @Override
    protected List<VDT> extraVdtTasks() {
        return List.of();
    }

    @Override
    protected boolean onVpaCreate(String vpa, String cardId) {
        return false;
    }

    @Override
    protected FICK.SESSION.PIN onCardSetPinPrepare(FICK.ACCOUNT.CARD card, VpaDetails vpaDetails, INPUT.PIN.SET.PREPARE input) {
        return null;
    }

    @Override
    protected boolean onCardSetPinConfirm(INPUT.PIN.SET.CONFIRM input, FICK.SESSION.PIN session) {
        return false;
    }

    @Override
    public PTM.RT getRT() {
        return (PTM.RT) this.runtimeParams;
    }

    @Override
    protected CompletableFuture<List<QueryTxnItem>> onQueryTransactions(UpckQueryOptions options) {
        return this.queryTxns(options);
    }

    private CompletableFuture<List<QueryTxnItem>> queryTxns(UpckQueryOptions options) {
        PtmHistParams params = new PtmHistParams();
        params.updateTime = options.beginTime;
        params.pageSize = options.records;
        params.pageNo = options.page;
        PaymentHistoryResponse resp = this.upiAccount.fetchPthListing(params);
        List<PaymentHistoryResponse.Transaction> txns = resp.data.txns;
        return null;
    }
}
