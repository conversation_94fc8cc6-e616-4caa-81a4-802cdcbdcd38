package fpi.upck.banks.in.paytm.nets;

import fpi.upck.banks.common.engine.net.UPN;
import fpi.upck.banks.in.paytm.apis.PcHKT;
import fpi.upck.banks.in.paytm.apis.PcPKT;

import java.util.*;

/**
 * Paytm专用网络请求选项类
 * 继承UPN.Options并添加Paytm特有的配置选项
 */
public class PtmNetOptions extends UPN.Options {
    /**
     * Paytm API业务类型
     */
    public PtmApiType ptmApiType = PtmApiType.AUTHENTICATED;
    /**
     * 请求序列号，用于生成动态headers
     */
    public long currentTime = System.currentTimeMillis();//请求包有时候会代入时间要素

    public boolean loginRequest;//是否为登录阶段的请求

    public List<PcPKT> paramKeys = new ArrayList<>();
    public List<PcHKT> headerKeys = new ArrayList<>();


    public void setParamKeys(PcPKT... keys) {
        this.paramKeys.addAll(Arrays.stream(keys).toList());
    }

    public void setHeaderKeys(PcHKT... keys) {
        this.headerKeys.addAll(Arrays.stream(keys).toList());
    }


}
