package fpi.upck.banks.in.paytm.models;

public enum PtmFetchStrategy {
    USERID("USERID"),
    USER_TYPE("USER_TYPE"),
    USER_ATTRIBUTE("USER_ATTRIBUTE"),
    password_status("password_status"),
    kyc_state("kyc_state"),
    enc_user_id("enc_user_id"),
    psp_deb_info("psp_deb_info"),
    external_user_id_b62("external_user_id_b62"),
    DEFAULT("DEFAULT");
    private String value;
    PtmFetchStrategy(String value) {
        this.value = value;
    }
}
