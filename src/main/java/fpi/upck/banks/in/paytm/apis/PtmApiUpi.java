package fpi.upck.banks.in.paytm.apis;

import fpi.upck.banks.UpckBankerImpl;
import fpi.upck.banks.common.engine.net.UPN;
import fpi.upck.banks.in.paytm.PtmDeviceUtils;
import fpi.upck.banks.in.paytm.apis.PcHKT;
import fpi.upck.banks.in.paytm.apis.PcPKT;
import fpi.upck.banks.in.paytm.entity.*;
import fpi.upck.banks.in.paytm.entity.upi.req.*;
import fpi.upck.banks.in.paytm.entity.upi.resp.*;
import fpi.upck.banks.in.paytm.models.txn.PtmHistParams;
import fpi.upck.banks.in.paytm.nets.PtmApiType;
import fpi.upck.banks.in.paytm.nets.PtmNetOptions;
import fpi.upck.utils.Utils;

/**
 * PtmApiUpi - Paytm UPI相关API接口类
 * 包含所有使用upi.paytm.com域名的PTM接口
 */
public class PtmApiUpi extends PtmApiBase {

    // 常量定义
    private static final String HTTPS_PREFIX = "https://";
    private static final String UPI_HOST = "upi.paytm.com";
    private static final String UPI_BASE = HTTPS_PREFIX + UPI_HOST;

    public PtmApiUpi(UpckBankerImpl banker) {
        super(banker);
    }

    // ==================== UPI相关接口 ====================

    /**
     * PtmApiUpi专用的请求处理方法
     * 实现链式处理：handleUpiCommonData -> requestPtmAsFetch
     *
     * @param options PtmNetOptions 请求配置
     * @return UPN.UpnResponse<T> 响应结果
     */
    protected <T> UPN.UpnResponse<T> requestPtmUpi(PtmNetOptions options) {

        // 第二层：调用基础的requestPtmAsFetch（会处理通用的公共参数）
        return this.requestPtmAsFetch(options);
    }

    private void handlePtmUpiHeaderAndParams(PtmNetOptions options) {
        options.setHeaderKeys(
                PcHKT.X_DEV_INTEGRITY_INT,      // x-dev-integrity-int
                PcHKT.CHANNEL,                  // channel
                PcHKT.X_APP_RID,                // x-app-rid
                PcHKT.X_NW,                     // x-nw
                PcHKT.ACCEPT_ENCODING,          // Accept-Encoding
                PcHKT.X_LATITUDE,               // x-latitude
                PcHKT.X_DEVICE_NAME,            // x-device-name
                PcHKT.X_SUBSCRIPTION_ID,        // x-subscription-id
                PcHKT.X_DEVICE_IDENTIFIER,      // x-device-identifier
                PcHKT.X_USER_TOKEN,             // x-user-token
                PcHKT.X_EPOCH,                  // x-epoch
                PcHKT.X_H5_VERSION,             // x-h5-version
                PcHKT.X_SHORT_DEVICE_ID,        // x-short-device-id
                PcHKT.X_DEVICE_MANUFACTURER,    // x-device-manufacturer
                PcHKT.X_LONGITUDE,              // x-longitude
                PcHKT.X_PHONE_NUMBER,           // x-phone-number
                PcHKT.X_COUNTRY_CODE,           // x-country-code
                PcHKT.CONTENT_TYPE,             // Content-Type
                PcHKT.X_APP_VERSION,            // x-app-version
                PcHKT.X_DEV_INTEGRITY_LICENSED, // x-dev-integrity-licensed
                PcHKT.X_DEV_INTEGRITY_TEMP,     // x-dev-integrity-temp
                PcHKT.X_SIM_SUB_ID,             // x-sim-sub-id
                PcHKT.X_DEB_STATUS,             // x-deb-status
                PcHKT.X_INTG,                   // x-intg
                PcHKT.X_CALL,                   // x-call
                PcHKT.X_TAMP,                   // x-tamp
                PcHKT.X_CLON,                   // x-clon
                PcHKT.X_INTG_SRC,               // x-intg-src
                PcHKT.X_LOC,                    // x-loc
                PcHKT.X_STORE,                  // x-store
                PcHKT.X_LOCINT,                 // x-locint
                PcHKT.X_ID,                     // x-id
                PcHKT.X_MFG,                    // x-mfg
                PcHKT.X_MODEL                   // x-model
        );

        // 通用Params - 使用setParamKeys
        options.setParamKeys(
                PcPKT.PLAY_STORE,               // playStore: true
                PcPKT.LATITUDE,                 // latitude: 1.4328325
                PcPKT.LOC_TIME,                 // locTime: 1750014489
                PcPKT.LANG_ID,                  // lang_id: 1
                PcPKT.LANGUAGE,                 // language: en
                PcPKT.LOCALE,                   // locale: en-IN
                PcPKT.DEVICE_NAME,              // deviceName: Pixel_2
                PcPKT.VERSION,                  // version: 10.58.4
                PcPKT.DEVICE_ID,                // deviceId: 1122554455bbffaa
                PcPKT.LONG,                     // long: 103.8333895
                PcPKT.DEVICE_IDENTIFIER,        // deviceIdentifier: Google-Pixel2-1122554455bbffaa
                PcPKT.OS_VERSION,               // osVersion: 11
                PcPKT.CLIENT,                   // client: androidapp
                PcPKT.RISK_DEVICE_ID,           // risk-device-id: Google-Pixel2-1122554455bbffaa
                PcPKT.DEVICE_MANUFACTURER,      // deviceManufacturer: Google
                PcPKT.NETWORK_TYPE,             // networkType: WIFI
                PcPKT.LAT,                      // lat: 1.4328325
                PcPKT.LONGITUDE,                // longitude: 103.8333895
                PcPKT.TIMESTAMP,                // timestamp: 1750014500801
                PcPKT.CHILD_SITE_ID,            // child_site_id: 1
                PcPKT.SITE_ID                   // site_id: 1
        );
    }

    /**
     * 余额查询接口
     * PTM接口: /pms/upi/ext/meta/app/v1/bal-inq
     *
     * @return BalInqResp 接口响应数据
     * @description POST upi.paytm.com/pms/upi/ext/meta/app/v1/bal-inq
     * @host upi.paytm.com
     * @method POST
     */
    public BalInqResp fetchBalInq(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/bal-inq";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        // 构建request body
        BalInqReq requestBody = new BalInqReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(BalInqResp.class);

        UPN.UpnResponse<BalInqResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 支付组合查询接口
     * PTM接口: /upi-pc/ext/v1/payment/combinations
     *
     * @return PaymentCombinationsResp 接口响应数据
     * @description GET upi.paytm.com/upi-pc/ext/v1/payment/combinations
     * @host upi.paytm.com
     * @method GET
     */
    public PaymentCombinationsResp fetchPaymentCombinations(String mVpa, String requestId) {
        String baseUrl = UPI_BASE + "/upi-pc/ext/v1/payment/combinations";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立Headers - 使用addHeader
        options.addHeader("upiLiteRegistered", "true");
        options.addHeader("modality", "OTHERS");
        options.addHeader("orderId", "null");
        options.addHeader("mVpa", mVpa);


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(PaymentCombinationsResp.class);

        UPN.UpnResponse<PaymentCombinationsResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 地址验证接口
     * PTM接口: /pms/upi/ext/meta/app/v1/validate/address
     *
     * @param vpa 要验证的VPA地址
     * @return ValidateAddressResp 接口响应数据
     * @description POST upi.paytm.com/pms/upi/ext/meta/app/v1/validate/address
     * @host upi.paytm.com
     * @method POST
     */
    public ValidateAddressResp fetchValidateAddress(String vpa, String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/validate/address";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        // 构建request body
        ValidateAddressReq requestBody = new ValidateAddressReq();
        requestBody.vpa = vpa;

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(ValidateAddressResp.class);

        UPN.UpnResponse<ValidateAddressResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 联系人档案接口
     * PTM接口: /upi/coms/ext/cms/v1/contact/profiles
     *
     * @return ContactProfilesResp 接口响应数据
     * @description POST upi.paytm.com/upi/coms/ext/cms/v1/contact/profiles
     * @host upi.paytm.com
     * @method POST
     */
    public ContactProfilesResp fetchContactProfiles() {
        String baseUrl = UPI_BASE + "/upi/coms/ext/cms/v1/contact/profiles";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 构建request body
        ContactProfilesReq requestBody = new ContactProfilesReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(ContactProfilesResp.class);

        UPN.UpnResponse<ContactProfilesResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 待处理交易接口
     * PTM接口: /bff/upi/ext/gateway/v3/transaction/pending/txn
     *
     * @return PendingTxnResp 接口响应数据
     * @description POST upi.paytm.com/bff/upi/ext/gateway/v3/transaction/pending/txn
     * @host upi.paytm.com
     * @method POST
     */
    public PendingTxnResp fetchPendingTxn(String requestId) {
        String baseUrl = UPI_BASE + "/bff/upi/ext/gateway/v3/transaction/pending/txn";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        // 构建request body
        PendingTxnReq requestBody = new PendingTxnReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(PendingTxnResp.class);

        UPN.UpnResponse<PendingTxnResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 交易状态查询接口
     * PTM接口: /txn/switch/upi/ext/txn/v6/transaction/status
     *
     * @return TransactionStatusResp 接口响应数据
     * @description GET upi.paytm.com/txn/switch/upi/ext/txn/v6/transaction/status
     * @host upi.paytm.com
     * @method GET
     */
    public TransactionStatusResp fetchTransactionStatus(String txnId, String requestId) {
        String baseUrl = UPI_BASE + "/txn/switch/upi/ext/txn/v6/transaction/status";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("modifyCtaPlacement", "true");
        options.param("multiLanguage", "true");
        options.param("requestId", requestId);
        options.param("channelCode", "paytm");
        options.param("requestTimestamp", String.valueOf(System.currentTimeMillis()));
        options.param("mobile", deviceUtils.getPhoneNumber91());
        options.param("txnId", txnId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(TransactionStatusResp.class);

        UPN.UpnResponse<TransactionStatusResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 用户档案接口
     * PTM接口: /pms/upi/ext/meta/app/v1/user/profile
     *
     * @return UserProfileResp 接口响应数据
     * @description GET upi.paytm.com/pms/upi/ext/meta/app/v1/user/profile
     * @host upi.paytm.com
     * @method GET
     */
    public UserProfileResp fetchUserProfile(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/user/profile";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();

        // 独立参数 - 使用param方法
        options.param("requestId", requestId);
        options.param("fetchUPICreditLineAccounts", "true");
        options.param("fetchCreditCardAccounts", "true");
        options.param("fetchPreferences", "true");
        options.param("fetchLRNDetails", "true");
        options.param("fetchInternationalisationDetails", "true");
        options.param("seq-no", requestId);
        options.param("cmIdInfoRequired", "true");

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(UserProfileResp.class);

        UPN.UpnResponse<UserProfileResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 用户可用性接口
     * PTM接口: /pms/upi/ext/meta/app/v1/user/availability
     *
     * @return UserAvailabilityResp 接口响应数据
     * @description GET upi.paytm.com/pms/upi/ext/meta/app/v1/user/availability
     * @host upi.paytm.com
     * @method GET
     */
    public UserAvailabilityResp fetchUserAvailability(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/user/availability";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(UserAvailabilityResp.class);

        UPN.UpnResponse<UserAvailabilityResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * PTH列表查询接口
     * PTM接口: /pth/ext/v3/listing
     *
     * @return PthListingResp 接口响应数据
     * @description POST upi.paytm.com/pth/ext/v3/listing
     * @host upi.paytm.com
     * @method POST
     */
    public PaymentHistoryResponse fetchPthListing(PtmHistParams input) {
        String baseUrl = UPI_BASE + "/pth/ext/v3/listing";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("pthVersion", PtmDeviceUtils.pthVersion);

        // 构建request body
        PthListingReq requestBody = new PthListingReq();
        requestBody.fromUpdatedDate = input.updateTime == null ? null : "" + input.updateTime;
        requestBody.pageNo = input.pageNo;
        requestBody.pageSize = "" + input.pageSize;

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(PaymentHistoryResponse.class);

        UPN.UpnResponse<PaymentHistoryResponse> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * UPI列表查询接口
     * PTM接口: /pth/ext/v3/upi/listing
     *
     * @return PthUpiListingResp 接口响应数据
     * @description POST upi.paytm.com/pth/ext/v3/upi/listing
     * @host upi.paytm.com
     * @method POST
     */
    public PthUpiListingResp fetchPthUpiListing() {
        String baseUrl = UPI_BASE + "/pth/ext/v3/upi/listing";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("pthVersion", "4.1");

        // 构建request body
        PthUpiListingReq requestBody = new PthUpiListingReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(PthUpiListingResp.class);

        UPN.UpnResponse<PthUpiListingResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 账户提供商查询接口
     * PTM接口: /pms/upi/ext/meta/app/v1/fetch/account-providers
     *
     * @return AccountProvidersResp 接口响应数据
     * @description GET upi.paytm.com/pms/upi/ext/meta/app/v1/fetch/account-providers
     * @host upi.paytm.com
     * @method GET
     */
    public AccountProvidersResp fetchAccountProviders(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/fetch/account-providers";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);
        options.param("topProviderCount", "12");

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(AccountProvidersResp.class);

        UPN.UpnResponse<AccountProvidersResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 余额查询状态检查接口
     * PTM接口: /pms/upi/ext/meta/app/v1/bal-inq/check/status
     *
     * @return BalInqCheckStatusResp 接口响应数据
     * @description GET upi.paytm.com/pms/upi/ext/meta/app/v1/bal-inq/check/status
     * @host upi.paytm.com
     * @method GET
     */
    public BalInqCheckStatusResp fetchBalInqCheckStatus(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/bal-inq/check/status";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(BalInqCheckStatusResp.class);

        UPN.UpnResponse<BalInqCheckStatusResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 密钥列表接口
     * PTM接口: /pms/upi/ext/meta/app/v1/list-keys
     *
     * @return ListKeysResp 接口响应数据
     * @description POST upi.paytm.com/pms/upi/ext/meta/app/v1/list-keys
     * @host upi.paytm.com
     * @method POST
     */
    public ListKeysResp fetchListKeys(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/list-keys";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("mobile", deviceUtils.getPhoneNumber91());
        options.param("requestId", requestId);

        // 构建request body
        ListKeysReq requestBody = new ListKeysReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(ListKeysResp.class);

        UPN.UpnResponse<ListKeysResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 获取令牌接口
     * PTM接口: /pms/upi/ext/meta/app/v1/get/token
     *
     * @return GetTokenResp 接口响应数据
     * @description POST upi.paytm.com/pms/upi/ext/meta/app/v1/get/token
     * @host upi.paytm.com
     * @method POST
     */
    public GetTokenResp fetchGetToken(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/get/token";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("requestId", requestId);

        // 构建request body
        GetTokenReq requestBody = new GetTokenReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(GetTokenResp.class);

        UPN.UpnResponse<GetTokenResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 获取令牌状态检查接口
     * PTM接口: /pms/upi/ext/meta/app/v1/get/token/checkStatus
     *
     * @return GetTokenCheckStatusResp 接口响应数据
     * @description GET upi.paytm.com/pms/upi/ext/meta/app/v1/get/token/checkStatus
     * @host upi.paytm.com
     * @method GET
     */
    public GetTokenCheckStatusResp fetchGetTokenCheckStatus(String requestId) {
        String baseUrl = UPI_BASE + "/pms/upi/ext/meta/app/v1/get/token/checkStatus";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("mobile", deviceUtils.getPhoneNumber91());
        options.param("requestId", requestId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(GetTokenCheckStatusResp.class);

        UPN.UpnResponse<GetTokenCheckStatusResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 账本工具接口
     * PTM接口: /passbook-accounts/ext/v2/instruments
     *
     * @return InstrumentsResp 接口响应数据
     * @description GET upi.paytm.com/passbook-accounts/ext/v2/instruments
     * @host upi.paytm.com
     * @method GET
     */
    public InstrumentsResp fetchInstruments() {
        String baseUrl = UPI_BASE + "/passbook-accounts/ext/v2/instruments";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("pbVersion", "7.2");

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(InstrumentsResp.class);

        UPN.UpnResponse<InstrumentsResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 元数据查询接口
     * PTM接口: /pth/ext/v1/metaData
     *
     * @return MetaDataResp 接口响应数据
     * @description GET upi.paytm.com/pth/ext/v1/metaData
     * @host upi.paytm.com
     * @method GET
     */
    public MetaDataResp fetchMetaData() {
        String baseUrl = UPI_BASE + "/pth/ext/v1/metaData";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("type", "search");

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(MetaDataResp.class);

        UPN.UpnResponse<MetaDataResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * UPI详情查询接口
     * PTM接口: /pth/ext/v3/UPI/detail
     *
     * @param txnId   交易ID
     * @param txnDate 交易日期
     * @return UpiDetailResp 接口响应数据
     * @description GET upi.paytm.com/pth/ext/v3/UPI/detail
     * @host upi.paytm.com
     * @method GET
     */
    public UpiDetailResp fetchUpiDetail(String txnId, String txnDate) {
        String baseUrl = UPI_BASE + "/pth/ext/v3/UPI/detail";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 独立参数 - 使用param方法
        options.param("openSource", "uth_listing");
        options.param("theme", "0");
        options.param("txnDate", txnDate);
        options.param("pthVersion", "4.1");
        options.param("txnId", txnId);

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(UpiDetailResp.class);

        UPN.UpnResponse<UpiDetailResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 通用支付请求异步接口
     * PTM接口: /txn/switch/upi/ext/txn/v3/common-pay-request-async
     *
     * @return CommonPayRequestAsyncResp 接口响应数据
     * @description POST upi.paytm.com/txn/switch/upi/ext/txn/v3/common-pay-request-async
     * @host upi.paytm.com
     * @method POST
     */
    public CommonPayRequestAsyncResp fetchCommonPayRequestAsync() {
        String baseUrl = UPI_BASE + "/txn/switch/upi/ext/txn/v3/common-pay-request-async";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 构建request body
        CommonPayRequestAsyncReq requestBody = new CommonPayRequestAsyncReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(CommonPayRequestAsyncResp.class);

        UPN.UpnResponse<CommonPayRequestAsyncResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 用户VPA详情查询接口
     * PTM接口: /upi-pc-profile/ext/v1/user/details/receiver_vpa
     *
     * @return ReceiverVpaResp 接口响应数据
     * @description GET upi.paytm.com/upi-pc-profile/ext/v1/user/details/receiver_vpa
     * @host upi.paytm.com
     * @method GET
     */
    public ReceiverVpaResp fetchReceiverVpa() {
        String baseUrl = UPI_BASE + "/upi-pc-profile/ext/v1/user/details/receiver_vpa";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(ReceiverVpaResp.class);

        UPN.UpnResponse<ReceiverVpaResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 添加联系人接口
     * PTM接口: /upi/coms/ext/cms/v1/contact/add
     *
     * @return ContactAddResp 接口响应数据
     * @description POST upi.paytm.com/upi/coms/ext/cms/v1/contact/add
     * @host upi.paytm.com
     * @method POST
     */
    public ContactAddResp fetchContactAdd() {
        String baseUrl = UPI_BASE + "/upi/coms/ext/cms/v1/contact/add";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        // 构建request body
        ContactAddReq requestBody = new ContactAddReq();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(ContactAddResp.class);

        UPN.UpnResponse<ContactAddResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * 手机号码映射接口
     * PTM接口: /upi-pc-profile/ext/v1/user/details/receiver_mobile_mapper
     *
     * @return ReceiverMobileMapperResp 接口响应数据
     * @description GET upi.paytm.com/upi-pc-profile/ext/v1/user/details/receiver_mobile_mapper
     * @host upi.paytm.com
     * @method GET
     */
    public ReceiverMobileMapperResp fetchReceiverMobileMapper() {
        String baseUrl = UPI_BASE + "/upi-pc-profile/ext/v1/user/details/receiver_mobile_mapper";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(ReceiverMobileMapperResp.class);

        UPN.UpnResponse<ReceiverMobileMapperResp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

    /**
     * v1版本账本工具接口
     * PTM接口: /passbook-accounts/ext/v1/instruments
     *
     * @return InstrumentsV1Resp 接口响应数据
     * @description GET upi.paytm.com/passbook-accounts/ext/v1/instruments
     * @host upi.paytm.com
     * @method GET
     */
    public InstrumentsV1Resp fetchInstrumentsV1() {
        String baseUrl = UPI_BASE + "/passbook-accounts/ext/v1/instruments";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();


        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(InstrumentsV1Resp.class);

        UPN.UpnResponse<InstrumentsV1Resp> resp = this.requestPtmUpi(options);
        return resp.body;
    }

}
