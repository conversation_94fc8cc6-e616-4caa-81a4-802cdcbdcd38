package fpi.upck.banks.in.paytm.models.dvbd;

import fpi.upck.banks.in.paytm.entity.BaseResp;
import fpi.upck.banks.in.paytm.models.BaseAccountResp;

/**
 * GetDevicebindingV2StatusSv1Resp - Response class
 * Generated from OkHttp3 logs in ptm.total.reprocessed_okhttp3.log
 * URL: https://accounts.paytm.com/devicebinding/v2/status/sv1
 */
public class DbdV2StatusSv1Resp extends BaseAccountResp<DbdV2StatusSv1Resp.Data> {

    /**
     * ResponseData - Main response data class
     */
    public static class Data {
        public String deviceBindingStatus;//: "CONFIRMED",
        public String sessionId;//: "$MWEZNJHHZMJMNJRMZTAXYZQ1NDE1MZKYMDI0MGMZNJQ",
        public Boolean debAutoReadOtpTriggered;//: false
    }
}
