package fpi.upck.banks.in.paytm.entity.upi;

import java.util.List;
import java.util.Map;

/**
 * UserProfileResp - UPI用户配置响应类
 * 基于真实JSON数据结构生成，不继承BaseResp
 * URL: https://upi.paytm.com/pms/upi/ext/meta/app/v1/user/profile
 */
public class UserProfileResp {
    
    public String status; // 响应状态
    public String seqNo; // 序列号
    public String respMessage; // 响应消息
    public String respCode; // 响应代码
    public RespDetails respDetails; // 响应详情
    public String requestId; // 请求ID
    
    /**
     * 响应详情类
     */
    public static class RespDetails {
        public ProfileDetail profileDetail; // 配置详情
        public MetaDetails metaDetails; // 元数据详情
    }
    
    /**
     * 配置详情类
     */
    public static class ProfileDetail {
        public List<VpaDetail> vpaDetails; // VPA详情列表
        public List<BindingDetail> bindingDetails; // 绑定详情列表
        public List<BankAccount> bankAccounts; // 银行账户列表
        public String profileStatus; // 配置状态
        public String upiLinkedMobileNumber; // UPI关联手机号
        public String countryCode; // 国家代码
        public List<CmIdDetail> cmIdDetails; // CM ID详情列表
        public List<String> inactiveVpa; // 非活跃VPA列表
        public Boolean isAnyAccMpinSet; // 是否设置了任何账户MPIN
        public Boolean isLiteOnboardEligible; // 是否符合Lite入驻条件
    }
    
    /**
     * VPA详情类
     */
    public static class VpaDetail {
        public String name; // VPA名称
        public String defaultAccRefId; // 默认账户引用ID
        public Boolean isPrimary; // 是否主要
        public Boolean isDefault; // 是否默认
        public String pspName; // PSP名称
        public String qrData; // 二维码数据
        public List<String> linkedAccRefIds; // 关联账户引用ID列表
    }
    
    /**
     * 绑定详情类
     */
    public static class BindingDetail {
        public String pspName; // PSP名称
        public Boolean isDeviceBinded; // 是否设备绑定
        public Boolean userOnboarded; // 用户是否已入驻
    }
    
    /**
     * 银行账户类
     */
    public static class BankAccount {
        public String bank; // 银行名称
        public String ifsc; // IFSC代码
        public String maskedAccountNumber; // 掩码账户号
        public String accountType; // 账户类型
        public String name; // 账户名称
        public String displayAccountNumber; // 显示账户号
        public Boolean isPrimary; // 是否主要账户
        public String prefferedVpa; // 首选VPA
        public String prefferedAccRefId; // 首选账户引用ID
        public Map<String, PspWiseDetail> pspWiseLinkedDetails; // PSP关联详情
        public List<PspToLink> pspToLink; // 待关联PSP列表
        public String txnAllowed; // 允许交易类型
        public String accPvdIfsc; // 账户提供商IFSC
        public String pgBankCode; // 支付网关银行代码
        public BankMetaData bankMetaData; // 银行元数据
        public String logo_url; // 银行Logo URL
        public Boolean isUpiCreditLine; // 是否UPI信用额度
    }
    
    /**
     * PSP关联详情类
     */
    public static class PspWiseDetail {
        public String vpa; // VPA地址
        public String pspName; // PSP名称
        public Boolean isLiteEligible; // 是否符合Lite条件
        public Boolean isLiteOfflineEligible; // 是否符合Lite离线条件
        public Boolean isSBMDEnabled; // 是否启用SBMD
        public Boolean isLiteAutopayEligible; // 是否符合Lite自动支付条件
        public String mpinSet; // MPIN设置状态
        public Boolean isAOTPSupportedOnBank; // 银行是否支持AOTP
        public Boolean isAsbaEnabled; // 是否启用ASBA
        public String accountUpdatedOn; // 账户更新时间
        public List<String> bankAccountFeatureList; // 银行账户功能列表
        public List<CredsAllowed> credsAllowed; // 允许的凭证列表
    }
    
    /**
     * 允许凭证类
     */
    public static class CredsAllowed {
        public String dlength; // 数据长度
        public String CredsAllowedDLength; // 允许凭证数据长度
        public String CredsAllowedDType; // 允许凭证数据类型
        public String CredsAllowedSubType; // 允许凭证子类型
        public String CredsAllowedType; // 允许凭证类型
        public String dLength; // 数据长度(备用字段)
    }
    
    /**
     * 待关联PSP类
     */
    public static class PspToLink {
        public String pspName; // PSP名称
        public String vpa; // VPA地址
        public Boolean isDeviceBinded; // 是否设备绑定
    }
    
    /**
     * 银行元数据类
     */
    public static class BankMetaData {
        public String perTxnLimit; // 单笔交易限额
        public BankHealth bankHealth; // 银行健康状态
    }
    
    /**
     * 银行健康状态类
     */
    public static class BankHealth {
        public String category; // 类别
        public String txnAction; // 交易动作
        public String displayMsg; // 显示消息
    }
    
    /**
     * CM ID详情类
     */
    public static class CmIdDetail {
        public String cmId; // CM ID
        public String cmIdType; // CM ID类型
        public String vpa; // VPA地址
        public String status; // 状态
        public Boolean inactiveFromProfile; // 是否从配置中非活跃
        public Long updatedOn; // 更新时间
    }
    
    /**
     * 元数据详情类
     */
    public static class MetaDetails {
        public String banksDown; // 银行停机信息
        public String npciHealthCategory; // NPCI健康类别
        public String npciHealthMsg; // NPCI健康消息
        public String txnAction; // 交易动作
    }
}
