package fpi.upck.banks.in.paytm.apis;

import fpi.upck.banks.UpckBankerImpl;
import fpi.upck.banks.common.engine.net.UPN;
import fpi.upck.banks.in.paytm.models.dvbd.DbdConfigSv1Resp;
import fpi.upck.banks.in.paytm.PtmDeviceUtils;
import fpi.upck.banks.in.paytm.entity.*;
import fpi.upck.banks.in.paytm.entity.acc.req.*;
import fpi.upck.banks.in.paytm.entity.acc.resp.Oauth2AuthorizeInitSv1Resp;
import fpi.upck.banks.in.paytm.entity.acc.resp.Oauth2V3TokenSv1Resp;
import fpi.upck.banks.in.paytm.models.dvbd.DbdVars;
import fpi.upck.banks.in.paytm.nets.PtmApiType;
import fpi.upck.banks.in.paytm.nets.PtmNetOptions;
import fpi.upck.utils.Utils;

// 导入重命名后的枚举类


/**
 * PtmApiAccount - Paytm账户相关API接口类
 * 包含所有使用accounts.paytm.com域名的PTM接口
 */
public class PtmApiAccount extends PtmApiBase {

    // 常量定义
    private static final String HTTPS_PREFIX = "https://";
    private static final String ACCOUNTS_HOST = "accounts.paytm.com";
    private static final String ACCOUNTS_BASE = HTTPS_PREFIX + ACCOUNTS_HOST;


    public PtmApiAccount(UpckBankerImpl banker) {
        super(banker);
    }

    // ==================== OAuth相关接口 ====================


    /**
     * PtmApiAccount专用的请求处理方法
     * 实现链式处理：handleAccountCommonData -> requestPtmAsFetch
     *
     * @param options PtmNetOptions 请求配置
     * @return UPN.UpnResponse<T> 响应结果
     */
    protected <T> UPN.UpnResponse<T> requestPtmAccount(PtmNetOptions options) {
        this.handlePtmAccHeaderAndParams(options);
        // 第二层：调用基础的requestPtmAsFetch（会处理通用的公共参数）
        return this.requestPtmAsFetch(options);
    }
    private void handlePtmAccHeaderAndParams(PtmNetOptions options){
        // 统一设置所有PTM Account接口的37个公共headers
        options.setHeaderKeys(
                PcHKT.ACCEPT_CHARSET,           // Accept-Charset
                PcHKT.X_LATITUDE,               // x-latitude
                PcHKT.X_DEVICE_NAME,            // x-device-name
                PcHKT.X_SUBSCRIPTION_ID,        // x-subscription-id
                PcHKT.X_OTPFALLBACK_MANDATE,    // x-otpfallback-mandate
                PcHKT.X_DEVICE_IDENTIFIER,      // x-device-identifier
                PcHKT.X_USER_TOKEN,             // x-user-token
                PcHKT.X_EPOCH,                  // x-epoch
                PcHKT.X_H5_VERSION,             // x-h5-version
                PcHKT.X_SHORT_DEVICE_ID,        // x-short-device-id
                PcHKT.AUTHORIZATION,            // Authorization
                PcHKT.X_DEVICE_MANUFACTURER,    // x-device-manufacturer
                PcHKT.X_LONGITUDE,              // x-longitude
                PcHKT.X_PHONE_NUMBER,           // x-phone-number
                PcHKT.X_COUNTRY_CODE,           // x-country-code
                PcHKT.CONTENT_TYPE,             // Content-Type
                PcHKT.X_APP_VERSION,            // x-app-version
                PcHKT.X_APP_RID,                // x-app-rid
                PcHKT.X_DEV_INTEGRITY_INT,      // x-dev-integrity-int
                PcHKT.X_DEV_INTEGRITY_LICENSED, // x-dev-integrity-licensed
                PcHKT.X_DEV_INTEGRITY_TEMP,     // x-dev-integrity-temp
                PcHKT.ACCEPT_ENCODING,          // Accept-Encoding
                PcHKT.X_SIM_SUB_ID,             // x-sim-sub-id
                PcHKT.X_DEB_STATUS,             // x-deb-status
                PcHKT.X_INTG,                   // x-intg
                PcHKT.X_CALL,                   // x-call
                PcHKT.X_TAMP,                   // x-tamp
                PcHKT.X_CLON,                   // x-clon
                PcHKT.X_INTG_SRC,               // x-intg-src
                PcHKT.X_LOC,                    // x-loc
                PcHKT.X_STORE,                  // x-store
                PcHKT.X_LOCINT,                 // x-locint
                PcHKT.X_NW,                     // x-nw
                PcHKT.X_ID,                     // x-id
                PcHKT.X_MFG,                    // x-mfg
                PcHKT.X_MODEL                   // x-model
        );

        // 统一设置所有PTM Account接口的12个基础参数
        options.setParamKeys(
                PcPKT.PLAY_STORE,           // playStore: true
                PcPKT.LANGUAGE,             // language: en
                PcPKT.LOCALE,               // locale: en-IN
                PcPKT.DEVICE_NAME,          // deviceName: Pixel_2
                PcPKT.VERSION,              // version: 10.58.4
                PcPKT.LONG,                 // long: 103.8333895
                PcPKT.DEVICE_IDENTIFIER,    // deviceIdentifier: Google-Pixel2-1122554455bbffaa
                PcPKT.OS_VERSION,           // osVersion: 11
                PcPKT.CLIENT,               // client: androidapp
                PcPKT.DEVICE_MANUFACTURER,  // deviceManufacturer: Google
                PcPKT.NETWORK_TYPE,         // networkType: WIFI
                PcPKT.LAT                   // lat: 1.4328325
        );
    }



    /**
     * OAuth2 V2认证接口
     * PTM接口: /oauth2/v2/authorize/sv1
     *
     * @param authenticationValue 认证值（OTP等）
     * @param stateToken          状态令牌
     * @return PostOauth2V2AuthorizeSv1Resp 接口响应数据
     * @description POST accounts.paytm.com/oauth2/v2/authorize/sv1
     * @host accounts.paytm.com
     * @method POST
     */
    public Oauth2V2AuthorizeSv1Resp fetchOauth2V2AuthorizeSv1(String authenticationValue, String stateToken) {
        String baseUrl = ACCOUNTS_BASE + "/oauth2/v2/authorize/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.OAUTH;



        // 构建request body
        Oauth2V2AuthorizeSv1Req requestBody = new Oauth2V2AuthorizeSv1Req();
        requestBody.authenticationValueType = "otp";
        requestBody.authenticationValue = authenticationValue;
        requestBody.stateToken = stateToken;
        requestBody.deviceId = deviceUtils.getDeviceIdentifier();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(Oauth2V2AuthorizeSv1Resp.class);

        UPN.UpnResponse<Oauth2V2AuthorizeSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * OAuth2 V3令牌接口
     * PTM接口: /oauth2/v3/token/sv1
     *
     * @param code 授权码
     * @return PostOauth2V3TokenSv1Resp 接口响应数据
     * @description POST accounts.paytm.com/oauth2/v3/token/sv1
     * @host accounts.paytm.com
     * @method POST
     */
    public Oauth2V3TokenSv1Resp fetchOauth2V3TokenSv1(String code) {
        String baseUrl = ACCOUNTS_BASE + "/oauth2/v3/token/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.OAUTH;



        // 构建request body
        Oauth2V3TokenSv1Req requestBody = new Oauth2V3TokenSv1Req();
        requestBody.grantType = "authorization_code";
        requestBody.code = code;
        requestBody.deviceId = deviceUtils.getDeviceIdentifier();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(Oauth2V3TokenSv1Resp.class);

        UPN.UpnResponse<Oauth2V3TokenSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }


    /**
     * OAuth认证接口
     * PTM接口: /oauth2/authorize/init/sv1
     *
     * @return PostOauth2AuthorizeInitSv1Resp 接口响应数据
     * @description POST accounts.paytm.com/oauth2/authorize/init/sv1
     * @host accounts.paytm.com
     * @method POST
     */
    public Oauth2AuthorizeInitSv1Resp fetchOauth2AuthorizeInitSv1(
            String stateToken) {
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        String authenticationId = deviceUtils.getPhoneNumber();
        String deviceId = deviceUtils.getDeviceIdentifier();
        //
        String baseUrl = ACCOUNTS_BASE + "/oauth2/authorize/init/sv1";
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.OAUTH;



        // 构建request body
        Oauth2AuthorizeInitSv1Req requestBody = new Oauth2AuthorizeInitSv1Req(authenticationId, deviceId, stateToken);

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(Oauth2AuthorizeInitSv1Resp.class);

        UPN.UpnResponse<Oauth2AuthorizeInitSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }


    /**
     * 设备绑定确认接口
     * PTM接口: /devicebinding/confirm/sv1
     *
     * @param sessionId 会话ID（从设备绑定初始化获取）
     * @param otpCode   OTP验证码
     * @return DbdConfirmSv1Resp 接口响应数据
     * @description POST accounts.paytm.com/devicebinding/confirm/sv1
     * @host accounts.paytm.com
     * @method POST
     */
    public DbdConfirmSv1Resp fetchDbdConfirmSv1(String sessionId, String otpCode) {
        String baseUrl = ACCOUNTS_BASE + "/devicebinding/confirm/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.DEVICE_BINDING;



        // 设置特定的headers
        options.addHeader("x-force-deb", "false");

        // 构建request body
        DbdConfirmSv1Req requestBody = new DbdConfirmSv1Req();
        requestBody.sessionId = sessionId;
        requestBody.method = "otp";
        requestBody.meta = new DbdConfirmSv1Req.Meta();
        requestBody.meta.phone = deviceUtils.getPhoneNumber();
        requestBody.meta.otp = otpCode;
        requestBody.initTimeEpoch = "" + deviceUtils.getLocTime();

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(DbdConfirmSv1Resp.class);

        UPN.UpnResponse<DbdConfirmSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    // ==================== 设备绑定相关接口 ====================


    /**
     * 设备绑定配置接口
     * PTM接口: /devicebinding/config/sv1
     *
     * @param vars DdbVars - 设备绑定变量，包含flow等参数
     * @return DbdConfigSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/devicebinding/config/sv1
     * @host accounts.paytm.com
     * @method GET
     * <p>
     * 根据HAR文件分析，此接口有3种不同的参数模式：
     * 1. MULTI_PSP_SILENT模式：包含pspId、locTime、lang_id参数
     * 2. multi_psp_deb模式：包含pspId参数，不含locTime、lang_id
     * 3. LOGIN_REGISTER模式：不含pspId、locTime、lang_id参数
     */
    public DbdConfigSv1Resp fetchDbdConfigSv1(DbdVars vars) {
        String baseUrl = ACCOUNTS_BASE + "/devicebinding/config/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        //--
        options.ptmApiType = PtmApiType.DEVICE_BINDING;



        options.addHeader("x-flow-name", vars.flowName);
        options.addHeader("x-vertical-name", vars.verticalName);
        options.addHeader("x-sub-reason", vars.subReason);
        options.addHeader("x-init-reason", "silent_deb");

        // 根据flow类型设置不同的参数组合
        String flow = Utils.or(vars.flow, "LOGIN_REGISTER");
        options.param("flow", flow);

        // MULTI_PSP_SILENT模式：包含完整参数集
        if ("MULTI_PSP_SILENT".equals(flow)) {
            options.setParamKeys(PcPKT.LOC_TIME);     // locTime: **********
            options.setParamKeys(PcPKT.LANG_ID);      // lang_id: 1
            // 使用vars中指定的pspId，如果未指定则默认使用ptyes
            String pspId = Utils.or(vars.pspId, "ptyes");
            options.param("pspId", pspId);
        }
        // multi_psp_deb模式：包含pspId但不含locTime、lang_id
        else if ("multi_psp_deb".equals(flow)) {
            // 使用vars中指定的pspId，如果未指定则默认使用ptsbi

            String pspId = Utils.or(vars.pspId, "ptsbi");
            options.param("pspId", pspId);
        }
        // LOGIN_REGISTER模式：最基础的参数集，不含pspId、locTime、lang_id
        // 其他模式使用默认参数集


        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(DbdConfigSv1Resp.class);
        UPN.UpnResponse<DbdConfigSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 设备绑定配置接口 - MULTI_PSP_SILENT模式
     * 用于静默多PSP设备绑定配置
     *
     * @param pspId PSP标识，如"ptyes"、"pthdfc"
     * @return DbdConfigSv1Resp 接口响应数据
     */
    public DbdConfigSv1Resp fetchDbdConfigSv1MultiPspSilent(String pspId) {
        DbdVars vars = DbdVars.createMultiPspSilent(pspId);
        return fetchDbdConfigSv1(vars);
    }

    /**
     * 设备绑定配置接口 - multi_psp_deb模式
     * 用于多PSP调试模式设备绑定配置
     *
     * @return DbdConfigSv1Resp 接口响应数据
     */
    public DbdConfigSv1Resp fetchDbdConfigSv1MultiPspDeb() {
        DbdVars vars = DbdVars.createMultiPspDeb();
        return fetchDbdConfigSv1(vars);
    }

    /**
     * 设备绑定配置接口 - LOGIN_REGISTER模式
     * 用于登录注册流程的设备绑定配置
     *
     * @return DbdConfigSv1Resp 接口响应数据
     */
    public DbdConfigSv1Resp fetchDbdConfigSv1LoginRegister() {
        DbdVars vars = DbdVars.createLoginRegister();
        return this.fetchDbdConfigSv1(vars);
    }

    /**
     * 设备绑定初始化接口
     * PTM接口: /devicebinding/init/sv1
     *
     * @param vars DbdVars - 设备绑定变量，包含flow等参数
     * @return DbdInitSv1Resp 接口响应数据
     * @description POST accounts.paytm.com/devicebinding/init/sv1
     * @host accounts.paytm.com
     * @method POST
     * <p>
     * 根据HAR文件分析，此接口有2种不同的模式：
     * 1. Silent模式：只有headers，没有request body（check_balance、silent_deb）
     * 2. Login模式：有完整的request body（login流程）
     */
    public DbdInitSv1Resp fetchDbdInitSv1(DbdVars vars) {
        String baseUrl = ACCOUNTS_BASE + "/devicebinding/init/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.DEVICE_BINDING;


        // 根据flow类型设置不同的参数组合
        String flow = Utils.or(vars.flow, "check_balance");
        String flowName = Utils.or(vars.flowName, flow);
        String subReason = Utils.or(vars.subReason, "");
        String initReason = Utils.or(vars.initReason, "");
        String verticalName = Utils.or(vars.verticalName, "TPAP_UPI");

        // 设置特定的headers
        options.addHeader("x-flow-name", flowName);
        options.addHeader("x-vertical-name", verticalName);
        options.addHeader("x-sub-reason", subReason);
        options.addHeader("x-init-reason", initReason);

        // 根据flow类型设置不同的参数和请求体
        if ("login".equals(flow)) {
            // Login模式：包含完整的request body
            options.setParamKeys(PcPKT.DEB_CARRIER_NAME);  // debCarrierName参数

            DbdInitSv1Req requestBody = new DbdInitSv1Req();
            requestBody.phoneNumber = Utils.or(vars.phoneNumber, deviceUtils.getPhoneNumber());
            requestBody.flow = flow;
            requestBody.method = Utils.or(vars.method, "otp");
            requestBody.deviceId = Utils.or(vars.deviceId, deviceUtils.getDeviceIdentifier());
            requestBody.integrityGroupId = Utils.generateUUID();
            requestBody.meta = new DbdInitSv1Req.Meta();
            options.jsonBody(requestBody);
        } else {
            // Silent模式：只有headers和query参数，没有request body
            if ("silent_deb".equals(flowName)) {
                // silent_deb模式包含locTime和lang_id参数
                options.setParamKeys(PcPKT.LOC_TIME);     // locTime: **********
                options.setParamKeys(PcPKT.LANG_ID);      // lang_id: 1
                options.setParamKeys(PcPKT.DEB_CARRIER_NAME);  // debCarrierName参数
            }
            // check_balance模式只有基础参数，不需要额外设置
        }

        options.url(baseUrl)
                .method(UPN.Method.POST)
                .respType(DbdInitSv1Resp.class);

        UPN.UpnResponse<DbdInitSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 设备绑定初始化接口 - check_balance模式
     * 用于余额查询的设备绑定初始化
     *
     * @return DbdInitSv1Resp 接口响应数据
     */
    public DbdInitSv1Resp fetchDbdInitSv1CheckBalance() {
        DbdVars vars = DbdVars.createCheckBalance();
        return this.fetchDbdInitSv1(vars);
    }

    /**
     * 设备绑定初始化接口 - silent_deb模式
     * 用于静默调试模式的设备绑定初始化
     *
     * @return DbdInitSv1Resp 接口响应数据
     */
    public DbdInitSv1Resp fetchDbdInitSv1SilentDeb() {
        DbdVars vars = DbdVars.createMultiPspSilent(null);
        return this.fetchDbdInitSv1(vars);
    }

    /**
     * 设备绑定初始化接口 - login模式
     * 用于登录流程的设备绑定初始化（包含完整请求体）
     *
     * @return DbdInitSv1Resp 接口响应数据
     */
    public DbdInitSv1Resp fetchDbdInitSv1Login() {
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        String deviceId = deviceUtils.getDeviceIdentifier();
        String phoneNumber = deviceUtils.getPhoneNumber();
        DbdVars vars = DbdVars.createLoginInit(phoneNumber, deviceId);
        return this.fetchDbdInitSv1(vars);
    }

    /**
     * 设备绑定状态查询接口
     * PTM接口: /devicebinding/v2/status/sv1
     *
     * @param sessionId String - 会话ID参数（可选，如果为null则使用默认值）
     * @param pspId     String - PSP ID参数（可选）
     * @return DbdV2StatusSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/devicebinding/v2/status/sv1
     * @host accounts.paytm.com
     * @method GET
     * <p>
     * 根据HAR文件分析，此接口支持多种参数模式：
     * 1. 包含sessionId和pspId的完整模式
     * 2. 基础查询模式
     */
    public DbdV2StatusSv1Resp fetchDbdV2StatusSv1(String sessionId, String pspId) {
        String baseUrl = ACCOUNTS_BASE + "/devicebinding/v2/status/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.DEVICE_BINDING;



        // 设置特定的headers
        options.addHeader("x-force-deb", "false");

        // 根据参数设置不同的查询参数
        if (sessionId != null && !sessionId.isEmpty()) {
            options.param("sessionId", sessionId);
        }

        if (pspId != null && !pspId.isEmpty()) {
            options.param("pspId", pspId);
            options.setParamKeys(PcPKT.LOC_TIME);  // 包含pspId时通常也包含locTime
        }

        // 固定参数
        options.param("isSmsSentCheck", "false")
                .param("isOutboxCheck", "false")
                .param("isSmscNoUsed", "false");

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(DbdV2StatusSv1Resp.class);

        UPN.UpnResponse<DbdV2StatusSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 设备绑定状态查询接口 - 简化版本
     *
     * @param sessionId 会话ID
     * @return DbdV2StatusSv1Resp 接口响应数据
     */
    public DbdV2StatusSv1Resp fetchDbdV2StatusSv1(String sessionId) {
        return fetchDbdV2StatusSv1(sessionId, null);
    }

    // ==================== OTP相关接口 ====================

    /**
     * OTP状态验证接口
     * PTM接口: /otp/v1/status
     *
     * @param transactionId 状态令牌（作为transactionId）
     * @return OtpStatusResp 接口响应数据
     * @description POST accounts.paytm.com/otp/v1/status
     * @host accounts.paytm.com
     * @method POST
     * <p>
     * 根据HAR文件分析，此接口只需要3个字段：transactionId, phoneNumber, actionType
     */
    public OtpStatusResp fetchOtpV1Status(String transactionId) {
        String baseUrl = ACCOUNTS_BASE + "/otp/v1/status";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.OTP;



        // 构建request body - 基于HAR文件分析，只需要3个字段
        OtpV1StatusReq requestBody = new OtpV1StatusReq();
        requestBody.transactionId = transactionId;
        requestBody.phoneNumber = deviceUtils.getPhoneNumber();
        requestBody.actionType = "Login_OTP_authorize";  // 根据HAR文件中的实际值

        options.url(baseUrl)
                .jsonBody(requestBody)
                .method(UPN.Method.POST)
                .respType(OtpStatusResp.class);

        UPN.UpnResponse<OtpStatusResp> resp = this.requestPtmAccount(options);
        return resp.body;
    }


    /**
     * 用户信息查询接口
     * PTM接口: /v2/user/sv1
     *
     * @param fetchStrategy 获取策略（可选）
     * @return V2UserSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v2/user/sv1
     * @host accounts.paytm.com
     * @method GET
     * <p>
     * 根据HAR文件分析，此接口支持多种参数模式：
     * 1. 包含locTime和lang_id的完整模式
     * 2. 基础查询模式
     */
    public V2UserSv1Resp fetchV2UserSv1(String fetchStrategy) {
        String baseUrl = ACCOUNTS_BASE + "/v2/user/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.AUTHENTICATED;



        // 根据fetchStrategy设置不同的参数
        if (fetchStrategy != null && !fetchStrategy.isEmpty()) {
            options.param("fetch_strategy", fetchStrategy);
        }

        // 某些模式包含额外参数
        if ("COMPLETE".equals(fetchStrategy)) {
            options.setParamKeys(PcPKT.LOC_TIME);     // locTime: **********
            options.setParamKeys(PcPKT.LANG_ID);      // lang_id: 1
            options.param("subscriptionIds", "");     // subscriptionIds参数
        }

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V2UserSv1Resp.class);

        UPN.UpnResponse<V2UserSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }


    /**
     * 钱包余额查询接口
     * PTM接口: /v1/user/wallet/balance/sv1
     *
     * @return V1userwalletbalanceSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/wallet/balance/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1userwalletbalanceSv1Resp getV1UserWalletBalanceSv1() {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/wallet/balance/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.AUTHENTICATED;
        // 特定headers
        options.addHeader("user_id", deviceUtils.getPtmUserId());

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1userwalletbalanceSv1Resp.class);

        UPN.UpnResponse<V1userwalletbalanceSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 银行账户查询接口
     * PTM接口: /v1/user/bank/accounts/sv1
     *
     * @return V1UserBankAccountsSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/bank/accounts/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserBankAccountsSv1Resp getV1UserBankAccountsSv1() {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/bank/accounts/sv1";

        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.AUTHENTICATED;

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserBankAccountsSv1Resp.class);



        // 特定headers
        options.addHeader("user_id", this.getDeviceUtils().getPtmUserId());

        UPN.UpnResponse<V1UserBankAccountsSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 用户卡片查询接口
     * PTM接口: /v1/user/cards/sv1
     *
     * @return V1UserCardsSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/cards/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserCardsSv1Resp getV1UserCardsSv1() {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/cards/sv1";
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();
        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.AUTHENTICATED;



        // 特定headers
        options.addHeader("user_id", deviceUtils.getPtmUserId());

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserCardsSv1Resp.class);

        UPN.UpnResponse<V1UserCardsSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 用户相关接口
     * PTM接口: /v1/user/upi/accounts/sv1
     *
     * @return V1UserUpiAccountsSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/upi/accounts/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserUpiAccountsSv1Resp getV1UserUpiAccountsSv1() {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/upi/accounts/sv1";

        PtmNetOptions options = new PtmNetOptions();
        options.ptmApiType = PtmApiType.AUTHENTICATED;

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserUpiAccountsSv1Resp.class);

        // Add specific headers for user UPI 账户
        options.addHeader("user_id", this.getDeviceUtils().getPtmUserId());
        // x-model已在公共参数中，无需重复添加

        UPN.UpnResponse<V1UserUpiAccountsSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }


    /**
     * 用户账本查询接口
     * PTM接口: /v1/user/passbook/sv1
     *
     * @param limit  String - 限制数量参数
     * @param offset String - 偏移量参数
     * @return V1UserPassbookSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/passbook/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserPassbookSv1Resp getV1UserPassbookSv1(String limit, String offset) {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/passbook/sv1";

        PtmNetOptions options = new PtmNetOptions();
        options.param("limit", limit)
                .param("offset", offset);

        options.ptmApiType = PtmApiType.AUTHENTICATED;

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserPassbookSv1Resp.class);

        // Add specific headers for user passbook
        options.addHeader("user_id", this.getDeviceUtils().getPtmUserId());
        // x-model已在公共参数中，无需重复添加

        UPN.UpnResponse<V1UserPassbookSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 用户通知查询接口
     * PTM接口: /v1/user/notifications/sv1
     *
     * @param limit  String - 限制数量参数
     * @param offset String - 偏移量参数
     * @return V1UserNotificationsSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/notifications/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserNotificationsSv1Resp getV1UserNotificationsSv1(String limit, String offset) {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/notifications/sv1";

        PtmNetOptions options = new PtmNetOptions();
        options.param("limit", limit)
                .param("offset", offset);

        options.ptmApiType = PtmApiType.AUTHENTICATED;

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserNotificationsSv1Resp.class);

        // Add specific headers for user notifications
        options.addHeader("user_id", this.getDeviceUtils().getPtmUserId());
        // x-model已在公共参数中，无需重复添加

        UPN.UpnResponse<V1UserNotificationsSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }

    /**
     * 用户优惠查询接口
     * PTM接口: /v1/user/offers/sv1
     *
     * @param limit  String - 限制数量参数
     * @param offset String - 偏移量参数
     * @return V1UserOffersSv1Resp 接口响应数据
     * @description GET accounts.paytm.com/v1/user/offers/sv1
     * @host accounts.paytm.com
     * @method GET
     */
    public V1UserOffersSv1Resp getV1UserOffersSv1(String limit, String offset) {
        String baseUrl = ACCOUNTS_BASE + "/v1/user/offers/sv1";

        PtmNetOptions options = new PtmNetOptions();
        options.param("limit", limit)
                .param("offset", offset);

        options.ptmApiType = PtmApiType.AUTHENTICATED;

        options.url(baseUrl)
                .method(UPN.Method.GET)
                .respType(V1UserOffersSv1Resp.class);

        options.addHeader("user_id", this.getDeviceUtils().getPtmUserId());

        UPN.UpnResponse<V1UserOffersSv1Resp> resp = this.requestPtmAccount(options);
        return resp.body;
    }



}
