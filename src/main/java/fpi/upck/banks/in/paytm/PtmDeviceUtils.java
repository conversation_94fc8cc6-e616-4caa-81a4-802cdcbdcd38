package fpi.upck.banks.in.paytm;

import fpi.upck.banks.UpckBankerImpl;
import fpi.upck.banks.common.engine.manager.IUpckDeviceUtils;
import fpi.upck.common.entity.FICK;
import fpi.upck.common.entity.device.FakeAndroid;
import fpi.upck.utils.Utils;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Random;

public class PtmDeviceUtils extends IUpckDeviceUtils {
    public final static String versionName = "10.58.4";
    public final static String versionCode = "721691";
    public final static String ptmClientApp = "androidapp";
    public final static String ptmXClientId= "APP_CLIENT";
    public final static String ptmClientId = "market-app";
    public final static String networkType = "4G";
    public final static String countryCode = "91";
    public final static String child_site_id = "1";
    public final static String lang_id = "1";
    public final static String site_id = "1";
    public final static String h5Version = "5.8.14";
    public final static String playStore = "true";
    public final static String mktPlaceApiKey = "7S4h-4jl4-115D";
    public final static String appHashHex = "3b26ad9e221f316686aaf1bf792c38042c18d489560febbc027f4084767ce925";
    public final static String packageName = "net.one97.paytm";
    public final static String channel = "app";
    public final static String appChannel = "APP";
    public final static String store = "1";
    public final static boolean debStatus = false;
    public final static String appSource = "android";
    public final static String pthVersion = "4.1";

    private int requestSeq;

    public PtmDeviceUtils(UpckBankerImpl banker) {
        super(banker);
        this.requestSeq = new Random().nextInt(1000);
    }

    @Override
    public PtmBanker getBanker() {
        return (PtmBanker) this.getImplBanker();
    }


    @Override
    public FICK.DEVICE onCreateFakedDevice(FakeAndroid fad) {
        PTM.DEVICE device = new PTM.DEVICE();
        device.h5VersionTime = System.currentTimeMillis();
        device.locTime = System.currentTimeMillis() + 1000L;
        Random random = new Random();
        device.fakeIp = String.format("100.100.%d.%d", random.nextInt(256), random.nextInt(256));
        return device;
    }

    public PTM.DEVICE getDeviceStorage() {
        return (PTM.DEVICE) super.getDeviceStorage();
    }

    public int getSeq() {
        return ++this.requestSeq;
    }

    public String getLocale() {
        var fad = this.getFAD();
        return fad.localeAbbr + "-" + fad.lang.toUpperCase();
    }

    public String getLang() {
        return this.getFAD().localeAbbr;
    }

    public String getDeviceName() {
        FakeAndroid fad = this.getFAD();
        return fad.buildModel.replaceAll(" ", "_");
    }

    public String getDeviceIdentifier() {
        //"Google-Pixel2-3e40474e56f38b6a"
        FakeAndroid fad = this.getFAD();
        var model = fad.buildModel.replaceAll(" ", "");
        return fad.buildManufacturer + "-" + model + fad.androidId;
    }

    public long getLocTime() {
        return this.getDeviceStorage().locTime;
    }

    public String getPtmUserId() {
        /*
        这个值从 https://accounts.paytm.com/v2/user/sv1 取回来的
         */
        return "**********";
    }

    public String getPhoneNumber() {
        return this.getBanker().getFpiAccount().phone;
    }

    public String getPhoneNumber91() {
        return "91" + this.getPhoneNumber();
    }

    public String getSubscriptionId() {
        return "4";
    }

    public String getXH5Version() {
        return PtmDeviceUtils.h5Version + "|" + (this.getDeviceStorage().h5VersionTime / 1000L);
    }

    public String getAuthReadHash() {
        return "asasK/GTt2i";
    }

    public String getBearerToken() {
        return "Bearer ";
    }

    public String getBasicToken() {
        /*
        accounts    域名使用Basic bWFya2V0LWFwcDo5YTA3MTc2Mi1hNDk5LTRiZDktOTE0YS00MzYxZTdjM2Y0YmM=
        hawkeye-api 域名使用:Basic YW5kcm9pZC1wcm9kOjRZWUVDdkNSSnhmTHcwVjJDM1ZNZWVpRUZoazdhSGV3
        还有一些使用真实的access token
        upi         域名使用{userToken=0bwjo3lde40mkl1tck2aszssjlsxwvtw2491}
         */
        return "Basic bWFya2V0LWFwcDo5YTA3MTc2Mi1hNDk5LTRiZDktOTE0YS00MzYxZTdjM2Y0YmM=";
    }




    

    public String getAccessToken() {
        PTM.AUTH.LOGIN loginData = this.getBanker().getAuthLoginData();
        return Objects.requireNonNull(loginData.oauthToken.tokens
                .stream().filter(t -> "paytm".equals(t.scope))
                .findAny().orElse(null)).accessToken;
    }

    public String generateRequestId() {
        return "PTM" + Utils.md5("" + System.currentTimeMillis());
    }

    public String generateSeqNo() {
        return "PTM" + Utils.md5("" + System.currentTimeMillis());
    }



    public String getMerchantId() {
        return "null";
    }



    public String generateHash() {
        return "";
    }

    /**
     * 生成设备指纹 (X_DFP)
     * 基于HAR文件分析的X_DFP结构实现
     * <p>
     * 格式: buildFingerprint|packageName|appVersion|userId|androidId|ptmUserId|dataDir|hashValue|gpuRenderer|playStore|debuggable
     * 示例: google/walleye/walleye:11/RP1A.201005.004/6782484:user/release-keys|net.one97.paytm|10.58.4|721691|1122554455bbffaa|**********|/data/user/0/net.one97.paytm/files|*********|3b26ad9e221f316686aaf1bf792c38042c18d489560febbc027f4084767ce925|Adreno (TM) 540|true|false
     *
     * @return Base64编码的设备指纹字符串
     */
    public String getDeviceFingerprint() {
        FakeAndroid fad = this.getFAD();
        // 组装设备指纹字符串
        String fingerprintData = String.join("|",
                fad.getFingerprint(),
                PtmDeviceUtils.packageName,
                PtmDeviceUtils.versionName,
                PtmDeviceUtils.versionCode,
                fad.androidId,
                this.getPtmUserId(),
                "/data/user/0/net.one97.paytm/files",
                PtmDeviceUtils.appHashHex,
                fad.gpuRenderer,
                "false",
                "false"
        );
        // Base64编码
        return Utils.base64Encode(fingerprintData.getBytes(StandardCharsets.UTF_8));
    }

    public String getFakeIp() {
        return this.getDeviceStorage().fakeIp;
    }


    /**
     * 创建带动态时间戳的请求ID
     * 格式: androidId:timestamp:1:sequence
     */
    public String getRid(long ts) {
        return String.format("%s:%d:1:%d", this.getFAD().androidId, ts, this.getSeq());
    }

    public String getXLocInt() {
        return "true," + (this.getLocTime() / 1000L) + ",true";
    }

    public String getEpoch() {
        return "" + System.currentTimeMillis() / 1000L;
    }

    public String getDebCarrierName() {
        return "";
    }


}
