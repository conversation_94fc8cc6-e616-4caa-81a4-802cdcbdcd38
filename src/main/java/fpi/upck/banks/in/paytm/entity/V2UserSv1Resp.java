package fpi.upck.banks.in.paytm.entity;

import java.util.List;

/**
 * V2UserSv1Resp - 用户信息查询响应类
 * 基于真实JSON数据结构重新生成
 * URL: https://accounts.paytm.com/v2/user/sv1
 *
 * 注意：此响应不符合BaseResp泛型转换规则，因为它直接返回用户数据，
 * 而不是包装在标准的 {status, responseCode, message, data} 结构中
 */
public class V2UserSv1Resp {

    public UserAttributeInfo userAttributeInfo; // 用户属性信息
    public Long userId; // 用户ID
    public List<String> userTypes; // 用户类型列表
    public MinKycDetails minKycDetails; // 最小KYC详情
    public DefaultInfo defaultInfo; // 默认用户信息
    public Boolean isPasswordExistent; // 是否存在密码
    public DeviceBindingInfo deviceBindingInfo; // 设备绑定信息
    public String enc_user_id; // 加密用户ID
    public String external_user_id_b62; // 外部用户ID (Base62编码)

    /**
     * 用户属性信息类
     */
    public static class UserAttributeInfo {
        public String IVR_FLAG; // IVR标志
        public String USER_TYPE; // 用户类型
        public String CUSTOMER_TYPE; // 客户类型
        public String BANK_CONSENT; // 银行同意书
        public String isCAeligible; // 是否CA合格
        public String SMS_FLAG; // SMS标志
    }

    /**
     * 最小KYC详情类
     */
    public static class MinKycDetails {
        public Boolean isMinKyc; // 是否最小KYC
        public String kycState; // KYC状态
    }

    /**
     * 默认用户信息类
     */
    public static class DefaultInfo {
        public String email; // 邮箱地址
        public String phone; // 手机号码
        public String countryCode; // 国家代码
        public String firstName; // 名字
        public String lastName; // 姓氏
        public String gender; // 性别
        public String displayName; // 显示名称
        public Boolean isKyc; // 是否KYC认证
        public Boolean emailVerificationStatus; // 邮箱验证状态
        public Boolean phoneVerificationStatus; // 手机验证状态
        public String customerStatus; // 客户状态
        public String loginStatus; // 登录状态
        public String customerCreationDate; // 客户创建日期
        public Boolean emailNotificationEnabled; // 邮件通知是否启用
        public Boolean internationalNumber; // 是否国际号码
    }

    /**
     * 设备绑定信息类
     */
    public static class DeviceBindingInfo {
        public String deviceBindingMethod; // 设备绑定方法
        public String deviceBindingTime; // 设备绑定时间
        public String deviceId; // 设备ID
        public String deviceModel; // 设备型号
        public String deviceOsVersion; // 设备操作系统版本
        public String isIntervene; // 是否干预
        public List<PspInfo> pspInfo; // PSP信息列表
    }

    /**
     * PSP信息类
     */
    public static class PspInfo {
        public String pspId; // PSP标识符
        public String subscriptionId; // 订阅ID
        public String deviceBindingTime; // 设备绑定时间
        public String isIntervene; // 是否干预
        public String pspDeviceId; // PSP设备ID
        public String pspCustId; // PSP客户ID
        public String appGenId; // 应用生成ID
        public String initialBindingTime; // 初始绑定时间
        public String isPseudoDeb; // 是否伪设备绑定
        public String verificationType; // 验证类型
    }
}
