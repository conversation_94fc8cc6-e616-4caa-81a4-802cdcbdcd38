package fpi.upck.banks.in.paytm.apis;

import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;

/**
 * PTM通用Header Key类型枚举 (Ptm Common Header Key Type)
 * 将所有127个header key定义为enum，优化字符串比较性能
 * 每个enum值都注释了对应的原始key name
 */
public enum PcHKT {
    ACCEPT,                    // "Accept"
    ACCEPT_CHARSET,            // "Accept-Charset"
    ACCEPT_ENCODING,           // "Accept-Encoding"
    ACCEPT_LANGUAGE,           // "Accept-Language"
//    CONTENT_TYPE,              // "Content-Type"
//    CONTENT_TYPE_LOWER,        // "content-type"


//    AUTHORIZATION,             // "Authorization"
//    AUTHORIZATION_LOWER,       // "authorization"
//    AUTHORIZATION_VALUE,       // "AUTHORIZATION_VALUE"

//    SESSION_TOKEN,             // "session-token"
//    SESSION_TOKEN_UNDERSCORE,  // "session_token"
//    SSO_TOKEN,                 // "sso-token"
//    SSO_TOKEN_UNDERSCORE,      // "sso_token"
//    SSOTOKEN,                  // "ssotoken"
//    USER_TOKEN,                // "usertoken"

    REQUEST_TOKEN,             // "Request-Token"
    REQUEST_TOKEN_LOWER,       // "request-token"

    DEVICE_ID,                 // "Device-Id"
    DEVICE_ID_LOWER,           // "deviceId"
    DEVICE_IDENTIFIER,         // "deviceIdentifier"
    OS,                        // "OS"
    OS_VERSION,                // "osVersion"
    X_DEVICE_IDENTIFIER,       // "x-device-identifier"
    X_DEVICE_MANUFACTURER,     // "x-device-manufacturer"
    X_DEVICE_NAME,             // "x-device-name"
    X_ID,                      // "x-id"
    X_MFG,                     // "x-mfg"
    X_MODEL,                   // "x-model"
    X_SHORT_DEVICE_ID,         // "x-short-device-id"
    LAT_LNG,                   // "Lat-Lng"
    X_LATITUDE,                // "x-latitude"
    X_LOC,                     // "x-loc"
    X_LOCINT,                  // "x-locint"
    X_LONGITUDE,               // "x-longitude"
    APP_CHANNEL,               // "app-channel"
    APP_VERSION,               // "appVersion"
    AUTO_READ_HASH,            // "autoReadHash"
    CHANNEL_TOKEN,             // "channel-token"
    CLIENT,                    // "client"
    SOURCE,                    // "source"
    X_APP_RID,                 // "x-app-rid"
    X_APP_VERSION,             // "x-app-version"
    X_COUNTRY_CODE,            // "x-country-code"
    X_EPOCH,                   // "x-epoch"
    X_H5_VERSION,              // "x-h5-version"
    LOCALE,                    // "locale"
    NETWORK,                   // "network"
    X_DEB_STATUS,              // "x-deb-status"
    X_GET_SERVER_RESPONSE_TIME, // "x-get-server-response-time"
    X_LOCALE,                  // "x-locale"
    X_NW,                      // "x-nw"
    X_SIM_SUB_ID,              // "x-sim-sub-id"
    X_STORE,                   // "x-store"
    X_TAMP,                    // "x-tamp"
    OTP_AUTOREAD,              // "otp-autoread"
    X_AUTH_UMP,                // "x-auth-ump"
    X_CONSENT_PSEUDO,          // "x-consent-pseudo"
    X_DEV_INTEGRITY_INT,       // "x-dev-integrity-int"
    X_DEV_INTEGRITY_LICENSED,  // "x-dev-integrity-licensed"
    X_DEV_INTEGRITY_TEMP,      // "x-dev-integrity-temp"
    X_INTG,                    // "x-intg"
    X_INTG_SRC,                // "x-intg-src"
    X_FORCE_DEB,               // "x-force-deb"
    X_MULTI_PSP_GROUPID,       // "x-multi-psp-groupid"
    X_MULTI_PSP_PRIMARY,       // "x-multi-psp-primary"
    X_PHONE_NUMBER,            // "x-phone-number"
    X_PSP_NAME,                // "x-psp-name"
    X_SUBSCRIPTION_ID,         // "x-subscription-id"
    VERTICAL_NAME,             // "verticalName"
    CLIENT_ID,                 // "client-id"
    CUST_ID,                   // "cust-id"
    USER_ID,                   // "user_id"
    USER_ID_CAMEL,             // "userId"
    USER_STAGE,                // "userStage"
    X_CALL,                    // "x-call"
    X_CLIENT_ID,               // "x-client-id"
    X_REQUESTER,               // "x-requester"
    X_USER_ID,                 // "x-user-id"
    X_USER_ID_UPPER,           // "X-USER-ID"
    X_USER_TOKEN,              // "x-user-token"
    API_ROLE,                  // "api_role"
    INSTRUMENT_TYPE,           // "instrumentType"
    MID,                       // "mid"
    MKTPLACE_APIKEY,           // "mktplace-apikey"
    MODALITY,                  // "modality"
    ORDER_ID,                  // "orderId"
    PULSE_CATEGORY,            // "pulseCategory"
    HASH,                      // "hash"
    RETRY_COUNT,               // "retryCount"
    TIMESTAMP,                 // "timestamp"
    X_CLON,                    // "x-clon"
    X_DFP,                     // "x-dfp"
    UPI_LITE_REGISTERED,       // "upiLiteRegistered"
    UPI_LITE_SUPPORTED,        // "upiLiteSupported"
    UPI_ORDER_DETAILS,         // "upiOrderDetails"
    ULTRA_MODE,                // "ultraMode"
//    CONTENT_ENCODING,          // "Content-Encoding"
    DATA,                      // "data"
    ENABLE_BOTF,               // "enable-botf"
    ENTRY_POINT,               // "entryPoint"
    IP,                        // "ip"
    LAST_PAYMENT_DETAILS,      // "lastPaymentDetails"
    OPEN_SOURCE                // "openSource"
    ;


    /**
     * 从header key name反向查找对应的enum类型
     * 这是keyName()方法的反向功能
     *
     * @param keyName header key name字符串
     * @return PchKT 对应的enum类型，如果未找到则返回null
     */
    public static PcHKT fromKeyName(String keyName) {
        if (keyName == null) {
            return null;
        }

        return switch (keyName) {
            // 基础HTTP Headers (6个)
            case "Accept" -> ACCEPT;
            case "Accept-Charset" -> ACCEPT_CHARSET;
            case "Accept-Encoding" -> ACCEPT_ENCODING;
            case "Accept-Language" -> ACCEPT_LANGUAGE;

//            case "Content-Type" -> CONTENT_TYPE;
//            case "content-type" -> CONTENT_TYPE_LOWER;


//            case "session-token" -> SESSION_TOKEN;
//            case "session_token" -> SESSION_TOKEN_UNDERSCORE;
//            case "sso-token" -> SSO_TOKEN;
//            case "sso_token" -> SSO_TOKEN_UNDERSCORE;
//            case "ssotoken" -> SSOTOKEN;
//            case "usertoken" -> USER_TOKEN;


            case "Request-Token" -> REQUEST_TOKEN;
            case "request-token" -> REQUEST_TOKEN_LOWER;

            // 设备相关Headers (12个)
            case "Device-Id" -> DEVICE_ID;
            case "deviceId" -> DEVICE_ID_LOWER;
            case "deviceIdentifier" -> DEVICE_IDENTIFIER;
            case "OS" -> OS;
            case "osVersion" -> OS_VERSION;
            case "x-device-identifier" -> X_DEVICE_IDENTIFIER;
            case "x-device-manufacturer" -> X_DEVICE_MANUFACTURER;
            case "x-device-name" -> X_DEVICE_NAME;
            case "x-id" -> X_ID;
            case "x-mfg" -> X_MFG;
            case "x-model" -> X_MODEL;
            case "x-short-device-id" -> X_SHORT_DEVICE_ID;

            // 位置相关Headers (5个)
            case "Lat-Lng" -> LAT_LNG;
            case "x-latitude" -> X_LATITUDE;
            case "x-loc" -> X_LOC;
            case "x-locint" -> X_LOCINT;
            case "x-longitude" -> X_LONGITUDE;

            // 应用相关Headers (12个)
            case "app-channel" -> APP_CHANNEL;
            case "appVersion" -> APP_VERSION;
            case "autoReadHash" -> AUTO_READ_HASH;
            case "channel-token" -> CHANNEL_TOKEN;
            case "client" -> CLIENT;
            case "source" -> SOURCE;
            case "x-app-rid" -> X_APP_RID;
            case "x-app-version" -> X_APP_VERSION;
            case "x-country-code" -> X_COUNTRY_CODE;
            case "x-epoch" -> X_EPOCH;
            case "x-h5-version" -> X_H5_VERSION;

            // 网络和状态Headers (10个)
            case "locale" -> LOCALE;
            case "network" -> NETWORK;
            case "x-deb-status" -> X_DEB_STATUS;
            case "x-get-server-response-time" -> X_GET_SERVER_RESPONSE_TIME;
            case "x-locale" -> X_LOCALE;
            case "x-nw" -> X_NW;
            case "x-sim-sub-id" -> X_SIM_SUB_ID;
            case "x-store" -> X_STORE;
            case "x-tamp" -> X_TAMP;

            // OTP和安全相关Headers (8个)
            case "otp-autoread" -> OTP_AUTOREAD;
            case "x-auth-ump" -> X_AUTH_UMP;
            case "x-consent-pseudo" -> X_CONSENT_PSEUDO;
            case "x-dev-integrity-int" -> X_DEV_INTEGRITY_INT;
            case "x-dev-integrity-licensed" -> X_DEV_INTEGRITY_LICENSED;
            case "x-dev-integrity-temp" -> X_DEV_INTEGRITY_TEMP;
            case "x-intg" -> X_INTG;
            case "x-intg-src" -> X_INTG_SRC;

            // 设备绑定相关Headers (9个)
            case "x-force-deb" -> X_FORCE_DEB;
            case "x-multi-psp-groupid" -> X_MULTI_PSP_GROUPID;
            case "x-multi-psp-primary" -> X_MULTI_PSP_PRIMARY;
            case "x-phone-number" -> X_PHONE_NUMBER;
            case "x-psp-name" -> X_PSP_NAME;
            case "x-subscription-id" -> X_SUBSCRIPTION_ID;
            case "verticalName" -> VERTICAL_NAME;

            // 用户相关Headers (12个)
            case "client-id" -> CLIENT_ID;
            case "cust-id" -> CUST_ID;
            case "user_id" -> USER_ID;
            case "userId" -> USER_ID_CAMEL;
            case "userStage" -> USER_STAGE;
//            case "verification_type" -> VERIFICATION_TYPE;
            case "x-call" -> X_CALL;
            case "x-client-id" -> X_CLIENT_ID;
            case "x-requester" -> X_REQUESTER;
            case "x-user-id" -> X_USER_ID;
            case "X-USER-ID" -> X_USER_ID_UPPER;
            case "x-user-token" -> X_USER_TOKEN;

            // 支付相关Headers (7个)
            case "api_role" -> API_ROLE;
            case "instrumentType" -> INSTRUMENT_TYPE;
            case "mid" -> MID;
            case "mktplace-apikey" -> MKTPLACE_APIKEY;
            case "modality" -> MODALITY;
            case "orderId" -> ORDER_ID;
            case "pulseCategory" -> PULSE_CATEGORY;

            // 时间和请求相关Headers (6个)
            case "hash" -> HASH;
            case "retryCount" -> RETRY_COUNT;
            case "timestamp" -> TIMESTAMP;
            case "x-clon" -> X_CLON;
            case "x-dfp" -> X_DFP;

            // UPI相关Headers (4个)
            case "upiLiteRegistered" -> UPI_LITE_REGISTERED;
            case "upiLiteSupported" -> UPI_LITE_SUPPORTED;
            case "upiOrderDetails" -> UPI_ORDER_DETAILS;
            case "ultraMode" -> ULTRA_MODE;

            // 其他Headers (8个)
//            case "Content-Encoding" -> CONTENT_ENCODING;
            case "data" -> DATA;
            case "enable-botf" -> ENABLE_BOTF;
            case "entryPoint" -> ENTRY_POINT;
            case "ip" -> IP;
            case "lastPaymentDetails" -> LAST_PAYMENT_DETAILS;
            case "openSource" -> OPEN_SOURCE;
            default -> null; // 未找到对应的enum，返回null
        };
    }


    /**
     * 将当前enum实例转换成真实的header key name
     *
     * @return String 对应的真实header key name
     */
    public String keyName() {
        return switch (this) {
            // 基础HTTP Headers (6个)
            case ACCEPT -> "Accept";
            case ACCEPT_CHARSET -> "Accept-Charset";
            case ACCEPT_ENCODING -> "Accept-Encoding";
            case ACCEPT_LANGUAGE -> "Accept-Language";

//            case CONTENT_TYPE -> "Content-Type";
//            case CONTENT_TYPE_LOWER -> "content-type";
//            case SESSION_TOKEN -> "session-token";
//            case SESSION_TOKEN_UNDERSCORE -> "session_token";
//            case SSO_TOKEN -> "sso-token";
//            case SSO_TOKEN_UNDERSCORE -> "sso_token";
//            case SSOTOKEN -> "ssotoken";
//            case USER_TOKEN -> "usertoken";

            case REQUEST_TOKEN -> "Request-Token";
            case REQUEST_TOKEN_LOWER -> "request-token";

            // 设备相关Headers (12个)
            case DEVICE_ID -> "Device-Id";
            case DEVICE_ID_LOWER -> "deviceId";
            case DEVICE_IDENTIFIER -> "deviceIdentifier";
            case OS -> "OS";
            case OS_VERSION -> "osVersion";
            case X_DEVICE_IDENTIFIER -> "x-device-identifier";
            case X_DEVICE_MANUFACTURER -> "x-device-manufacturer";
            case X_DEVICE_NAME -> "x-device-name";
            case X_ID -> "x-id";
            case X_MFG -> "x-mfg";
            case X_MODEL -> "x-model";
            case X_SHORT_DEVICE_ID -> "x-short-device-id";

            // 位置相关Headers (5个)
            case LAT_LNG -> "Lat-Lng";
            case X_LATITUDE -> "x-latitude";
            case X_LOC -> "x-loc";
            case X_LOCINT -> "x-locint";
            case X_LONGITUDE -> "x-longitude";

            // 应用相关Headers (12个)
            case APP_CHANNEL -> "app-channel";
            case APP_VERSION -> "appVersion";
            case AUTO_READ_HASH -> "autoReadHash";
            case CHANNEL_TOKEN -> "channel-token";
            case CLIENT -> "client";
            case SOURCE -> "source";
            case X_APP_RID -> "x-app-rid";
            case X_APP_VERSION -> "x-app-version";
            case X_COUNTRY_CODE -> "x-country-code";
            case X_EPOCH -> "x-epoch";
            case X_H5_VERSION -> "x-h5-version";

            // 网络和状态Headers (10个)
            case LOCALE -> "locale";
            case NETWORK -> "network";
            case X_DEB_STATUS -> "x-deb-status";
            case X_GET_SERVER_RESPONSE_TIME -> "x-get-server-response-time";
            case X_LOCALE -> "x-locale";
            case X_NW -> "x-nw";
            case X_SIM_SUB_ID -> "x-sim-sub-id";
            case X_STORE -> "x-store";
            case X_TAMP -> "x-tamp";

            // OTP和安全相关Headers (8个)
            case OTP_AUTOREAD -> "otp-autoread";
            case X_AUTH_UMP -> "x-auth-ump";
            case X_CONSENT_PSEUDO -> "x-consent-pseudo";
            case X_DEV_INTEGRITY_INT -> "x-dev-integrity-int";
            case X_DEV_INTEGRITY_LICENSED -> "x-dev-integrity-licensed";
            case X_DEV_INTEGRITY_TEMP -> "x-dev-integrity-temp";
            case X_INTG -> "x-intg";
            case X_INTG_SRC -> "x-intg-src";

            // 设备绑定相关Headers (12个)
            case X_FORCE_DEB -> "x-force-deb";
            case X_MULTI_PSP_GROUPID -> "x-multi-psp-groupid";
            case X_MULTI_PSP_PRIMARY -> "x-multi-psp-primary";
            case X_PHONE_NUMBER -> "x-phone-number";
            case X_PSP_NAME -> "x-psp-name";
            case X_SUBSCRIPTION_ID -> "x-subscription-id";
            case VERTICAL_NAME -> "verticalName";
            // 用户相关Headers (12个)
            case CLIENT_ID -> "client-id";
            case CUST_ID -> "cust-id";
            case USER_ID -> "user_id";
            case USER_ID_CAMEL -> "userId";
            case USER_STAGE -> "userStage";
//            case VERIFICATION_TYPE -> "verification_type";
            case X_CALL -> "x-call";
            case X_CLIENT_ID -> "x-client-id";
            case X_REQUESTER -> "x-requester";
            case X_USER_ID -> "x-user-id";
            case X_USER_ID_UPPER -> "X-USER-ID";
            case X_USER_TOKEN -> "x-user-token";

            // 支付相关Headers (22个)
            case API_ROLE -> "api_role";
            case INSTRUMENT_TYPE -> "instrumentType";
            case MID -> "mid";
            case MKTPLACE_APIKEY -> "mktplace-apikey";
            case MODALITY -> "modality";
            case ORDER_ID -> "orderId";
            case PULSE_CATEGORY -> "pulseCategory";

            // 时间和请求相关Headers (6个)
            case HASH -> "hash";
            case RETRY_COUNT -> "retryCount";
            case TIMESTAMP -> "timestamp";
            case X_CLON -> "x-clon";
            case X_DFP -> "x-dfp";

            // UPI相关Headers (4个)
            case UPI_LITE_REGISTERED -> "upiLiteRegistered";
            case UPI_LITE_SUPPORTED -> "upiLiteSupported";
            case UPI_ORDER_DETAILS -> "upiOrderDetails";
            case ULTRA_MODE -> "ultraMode";

            // 其他Headers (8个)
//            case CONTENT_ENCODING -> "Content-Encoding";
            case DATA -> "data";
            case ENABLE_BOTF -> "enable-botf";
            case ENTRY_POINT -> "entryPoint";
            case IP -> "ip";
            case LAST_PAYMENT_DETAILS -> "lastPaymentDetails";
            case OPEN_SOURCE -> "openSource";

            default -> throw new UpckException(UpckErrCode.ERR_DEV_FATAL("UnknownKeyType:" + this));
        };
    }

}
