package fpi.upck.banks.in.paytm.models.oauth2;

import fpi.upck.banks.in.paytm.models.BaseAccountResp;

/**
 * PostOauth2AuthorizeInitSv1Resp - Response class for POST /oauth2/authorize/init/sv1
 * 基于HAR文件分析生成的OAuth2认证初始化响应类
 * 
 * 响应体示例:
 * {
 *   "responseCode": "BE1400001",
 *   "message": "SUCCESS",
 *   "status": "SUCCESS",
 *   "data": {
 *     "stateToken": "$NzA2ZDhhNDk3ODNiZGE4ZTE2NTYwNTA0NWY1NWVhZWY",
 *     "authenticationValueType": "NONE"
 *   }
 * }
 */
public class Oauth2V2AuthorizeInitSv1Resp extends BaseAccountResp<Oauth2V2AuthorizeInitSv1Resp.Data> {

    public static class Data {
        public String stateToken;
        public String authenticationValueType;

    }


}
