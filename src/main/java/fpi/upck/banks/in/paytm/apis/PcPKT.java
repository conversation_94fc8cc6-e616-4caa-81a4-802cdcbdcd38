package fpi.upck.banks.in.paytm.apis;

import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;

/**
 * PTM通用参数Key类型枚举 (Ptm Common Param Key Type)
 * 将所有70个param key定义为enum，优化字符串比较性能
 * 每个enum值都注释了对应的原始key name
 */
public enum PcPKT {

    APP_ID,                    // "app_id"
    APP_VERSION,               // "appVersion"
    CHANNEL,                   // "channel"
    CHANNEL_CODE,              // "channelCode"
    CHILD_SITE_ID,             // "child_site_id"
    CLIENT,                    // "client"
    DEVICE_IDENTIFIER,         // "deviceIdentifier"
    DEVICE_MANUFACTURER,       // "deviceManufacturer"
    DEVICE_NAME,               // "deviceName"
    NETWORK_TYPE,              // "networkType"
    OPEN_SOURCE,               // "openSource"
    OS,                        // "os"
    OS_VERSION,                // "osVersion"
    PLAY_STORE,                // "playStore"
    SITE_ID,                   // "site_id"
    VERSION,                   // "version"
    LAT,                       // "lat"
    LATITUDE,                  // "latitude"
    LOC_TIME,                  // "locTime"
    LONG,                      // "long"
    LONGITUDE,                 // "longitude"

    LANG_ID,                   // "lang_id"
    LANGUAGE,                  // "language"
    LOCALE,                    // "locale"

    SESSION_ID,                // "sessionId"

    DEB_CARRIER_NAME,          // "debCarrierName"
    CUSTOMER_ID,               // "customer_id"
    FETCH_STRATEGY,            // "fetch_strategy"
    MOBILE,                    // "mobile"

    SHOW_PINNED_CHATS,         // "showPinnedChats"
    TOP_PROVIDER_COUNT,        // "topProviderCount"

    REQUEST_TIMESTAMP,         // "requestTimestamp"
    TIMESTAMP,                 // "timestamp"
    TIMEZONE,                  // "timezone"
    TXN_DATE,                  // "txnDate"

    DEVICE,                    // "device"
    DEVICE_ID,                 // "deviceId"
    RESOLUTION,                // "resolution"
    RISK_DEVICE_ID,            // "risk-device-id"
    SIM_SUBSCRIPTION_ID,       // "simSubscriptionId"

    CM_ID_INFO_REQUIRED,       // "cmIdInfoRequired"
    FETCH_CREDIT_CARD_ACCOUNTS, // "fetchCreditCardAccounts"
    FETCH_INTERNATIONALISATION_DETAILS, // "fetchInternationalisationDetails"
    FETCH_LRN_DETAILS,         // "fetchLRNDetails"
    FETCH_NEW_POLL_CONFIG,     // "fetchNewPollConfig"
    FETCH_PREFERENCES,         // "fetchPreferences"
    FETCH_UPI_CREDIT_LINE_ACCOUNTS, // "fetchUPICreditLineAccounts"
    MODIFY_CTA_PLACEMENT,      // "modifyCtaPlacement"
    MULTI_LANGUAGE,            // "multiLanguage"
    NON_DEFAULT_BANK_ACCOUNTS, // "nonDefaultBankAccounts"
    PREFERENCE_KEYS,           // "preferenceKeys"
    SERVICE,                   // "service"

    PB_VERSION,                // "pbVersion"
    PC_VERSION,                // "pcVersion"
    PTH_VERSION,               // "pthVersion"
    TYPE,                      // "type"

    OFFER_TAG,                 // "offer_tag"
    THEME,                     // "theme"
    TXN_ID                     // "txnId"
    ;

    /**
     * 将当前enum实例转换成真实的key name
     *
     * @return String 对应的真实key name
     */
    public String keyName() {
        return switch (this) {
            case APP_ID -> "app_id";
            case APP_VERSION -> "appVersion";
            case CHANNEL -> "channel";
            case CHANNEL_CODE -> "channelCode";
            case CHILD_SITE_ID -> "child_site_id";
            case CLIENT -> "client";
            case DEVICE_IDENTIFIER -> "deviceIdentifier";
            case DEVICE_MANUFACTURER -> "deviceManufacturer";
            case DEVICE_NAME -> "deviceName";
            case NETWORK_TYPE -> "networkType";
            case OPEN_SOURCE -> "openSource";
            case OS -> "os";
            case OS_VERSION -> "osVersion";
            case PLAY_STORE -> "playStore";
            case SITE_ID -> "site_id";
            case VERSION -> "version";
            case LAT -> "lat";
            case LATITUDE -> "latitude";
            case LOC_TIME -> "locTime";
            case LONG -> "long";
            case LONGITUDE -> "longitude";
            case LANG_ID -> "lang_id";
            case LANGUAGE -> "language";
            case LOCALE -> "locale";
            case SESSION_ID -> "sessionId";
            case DEB_CARRIER_NAME -> "debCarrierName";
            case CUSTOMER_ID -> "customer_id";
            case FETCH_STRATEGY -> "fetch_strategy";
            case MOBILE -> "mobile";
            case SHOW_PINNED_CHATS -> "showPinnedChats";
            case TOP_PROVIDER_COUNT -> "topProviderCount";
            case REQUEST_TIMESTAMP -> "requestTimestamp";
            case TIMESTAMP -> "timestamp";
            case TIMEZONE -> "timezone";
            case TXN_DATE -> "txnDate";
            case DEVICE -> "device";
            case DEVICE_ID -> "deviceId";
            case RESOLUTION -> "resolution";
            case RISK_DEVICE_ID -> "risk-device-id";
            case SIM_SUBSCRIPTION_ID -> "simSubscriptionId";
            case CM_ID_INFO_REQUIRED -> "cmIdInfoRequired";
            case FETCH_CREDIT_CARD_ACCOUNTS -> "fetchCreditCardAccounts";
            case FETCH_INTERNATIONALISATION_DETAILS -> "fetchInternationalisationDetails";
            case FETCH_LRN_DETAILS -> "fetchLRNDetails";
            case FETCH_NEW_POLL_CONFIG -> "fetchNewPollConfig";
            case FETCH_PREFERENCES -> "fetchPreferences";
            case FETCH_UPI_CREDIT_LINE_ACCOUNTS -> "fetchUPICreditLineAccounts";
            case MODIFY_CTA_PLACEMENT -> "modifyCtaPlacement";
            case MULTI_LANGUAGE -> "multiLanguage";
            case NON_DEFAULT_BANK_ACCOUNTS -> "nonDefaultBankAccounts";
            case PREFERENCE_KEYS -> "preferenceKeys";
            case SERVICE -> "service";
            case PB_VERSION -> "pbVersion";
            case PC_VERSION -> "pcVersion";
            case PTH_VERSION -> "pthVersion";
            case TYPE -> "type";
            case OFFER_TAG -> "offer_tag";
            case THEME -> "theme";
            case TXN_ID -> "txnId";

            default -> throw new UpckException(UpckErrCode.ERR_DEV_FATAL("UnknownKeyType:" + this));
        };

    }

    /**
     * 从param key name反向查找对应的enum类型
     * 这是keyName()方法的反向功能
     *
     * @param keyName param key name字符串
     * @return PcpKT 对应的enum类型，如果未找到则返回null
     */
    public static PcPKT fromKeyName(String keyName) {
        if (keyName == null) {
            return null;
        }

        return switch (keyName) {
            case "app_id" -> APP_ID;
            case "appVersion" -> APP_VERSION;
            case "channel" -> CHANNEL;
            case "channelCode" -> CHANNEL_CODE;
            case "child_site_id" -> CHILD_SITE_ID;
            case "client" -> CLIENT;
            case "deviceIdentifier" -> DEVICE_IDENTIFIER;
            case "deviceManufacturer" -> DEVICE_MANUFACTURER;
            case "deviceName" -> DEVICE_NAME;
            case "networkType" -> NETWORK_TYPE;
            case "openSource" -> OPEN_SOURCE;
            case "os" -> OS;
            case "osVersion" -> OS_VERSION;
            case "playStore" -> PLAY_STORE;
            case "site_id" -> SITE_ID;
            case "version" -> VERSION;
            case "lat" -> LAT;
            case "latitude" -> LATITUDE;
            case "locTime" -> LOC_TIME;
            case "long" -> LONG;
            case "longitude" -> LONGITUDE;
            case "lang_id" -> LANG_ID;
            case "language" -> LANGUAGE;
            case "locale" -> LOCALE;
            case "sessionId" -> SESSION_ID;
            case "debCarrierName" -> DEB_CARRIER_NAME;
            case "customer_id" -> CUSTOMER_ID;
            case "fetch_strategy" -> FETCH_STRATEGY;
            case "mobile" -> MOBILE;
            case "showPinnedChats" -> SHOW_PINNED_CHATS;
            case "topProviderCount" -> TOP_PROVIDER_COUNT;
            case "requestTimestamp" -> REQUEST_TIMESTAMP;
            case "timestamp" -> TIMESTAMP;
            case "timezone" -> TIMEZONE;
            case "txnDate" -> TXN_DATE;
            case "device" -> DEVICE;
            case "deviceId" -> DEVICE_ID;
            case "resolution" -> RESOLUTION;
            case "risk-device-id" -> RISK_DEVICE_ID;
            case "simSubscriptionId" -> SIM_SUBSCRIPTION_ID;
            case "cmIdInfoRequired" -> CM_ID_INFO_REQUIRED;
            case "fetchCreditCardAccounts" -> FETCH_CREDIT_CARD_ACCOUNTS;
            case "fetchInternationalisationDetails" -> FETCH_INTERNATIONALISATION_DETAILS;
            case "fetchLRNDetails" -> FETCH_LRN_DETAILS;
            case "fetchNewPollConfig" -> FETCH_NEW_POLL_CONFIG;
            case "fetchPreferences" -> FETCH_PREFERENCES;
            case "fetchUPICreditLineAccounts" -> FETCH_UPI_CREDIT_LINE_ACCOUNTS;
            case "modifyCtaPlacement" -> MODIFY_CTA_PLACEMENT;
            case "multiLanguage" -> MULTI_LANGUAGE;
            case "nonDefaultBankAccounts" -> NON_DEFAULT_BANK_ACCOUNTS;
            case "preferenceKeys" -> PREFERENCE_KEYS;
            case "service" -> SERVICE;
            case "pbVersion" -> PB_VERSION;
            case "pcVersion" -> PC_VERSION;
            case "pthVersion" -> PTH_VERSION;
            case "type" -> TYPE;
            case "offer_tag" -> OFFER_TAG;
            case "theme" -> THEME;
            case "txnId" -> TXN_ID;
            default -> null; // 未找到对应的enum，返回null
        };
    }
}