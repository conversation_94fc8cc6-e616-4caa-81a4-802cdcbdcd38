package fpi.upck.banks.in.paytm.apis;

import com.google.gson.JsonObject;
import fpi.upck.banks.UpckBankerImpl;
import fpi.upck.banks.common.engine.net.UPN;
import fpi.upck.banks.common.engine.net.UpbBankerNet;
import fpi.upck.banks.in.paytm.PtmDeviceUtils;
import fpi.upck.banks.in.paytm.nets.PtmNetOptions;
import fpi.upck.common.entity.device.FakeAndroid;
import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;
import fpi.upck.common.entity.network.UpckNetType;
import fpi.upck.utils.Utils;
import okhttp3.OkHttpClient;

import java.util.concurrent.CompletableFuture;

public abstract class PtmApiBase extends UpbBankerNet {


    public PtmApiBase(UpckBankerImpl banker) {
        super(banker);
    }


    public PtmDeviceUtils getDeviceUtils() {
        return (PtmDeviceUtils) this.banker.getDeviceUtils();
    }

    protected <T> UPN.UpnResponse<T> requestPtmAsFetch(PtmNetOptions options, Class<T> clazz) {
        options.respType(clazz);
        return this.requestPtmAsFetch(options);
    }

    protected <T> UPN.UpnResponse<T> requestPtmAsFetch(PtmNetOptions options) {
        PtmDeviceUtils du = this.getDeviceUtils();
        this.handleAllHeaderAndParams(options);
        options.ignoreAutoInjectHost = true;
        if (!options.loginRequest) {
            /*
            排除登录请求,发包前设置access token
             */
            options.resetAccessToken(du);
        }

        CompletableFuture<UpnResponse<T>> future = this.postAsFuture(options);
        return future.thenApply((response) -> this.onParseJsonResponse(response, options))
                .join();
    }

    private void handleAllHeaderAndParams(PtmNetOptions options) {
        PtmDeviceUtils deviceUtils = this.getDeviceUtils();


        //处理所有的header
        var headers = options.headers();
        options.headerKeys.forEach(type -> {
            String key = type.keyName();
            String v = headers.get(key);
            if (v != null) return;
            v = this.getHeaderKey(type, options);
            if (v == null) return;
            headers.put(key, v);
        });

        //处理所有的param
        var params = options.params();
        options.paramKeys.forEach(type -> {
            String key = type.keyName();
            String v = headers.get(key);
            if (v != null) return;
            v = this.getParamKey(type, options);
            if (v == null) return;
            params.put(key, v);
        });


    }

    public <T> UPN.UpnResponse<T> parseHttpCodeError(UPN.UpnResponse<T> resp, PtmNetOptions options) {
        int statusCode = resp.code;
        if (statusCode >= 200 && statusCode < 400)
            return resp;
        JsonObject errorBody;
        try {
            errorBody = Utils.gson.fromJson(resp.rawBody, JsonObject.class);
        } catch (Exception e) {
            throw this.banker.throwUnknownError(String.format("code=%d,raw html=%s", resp.code, resp.rawBody));
        }
        String responseCode = Utils.jsonPathAsString(errorBody, "responseCode");
        String errMsg = Utils.jsonPathAsString(errorBody, "message");
        if (statusCode == 410 && "530".equals(responseCode) && "Invalid Token".equals(errMsg)) {
            throw UpckErrCode.ERR_UNAUTHORIZED();
        }
        throw this.banker.throwUnknownError(String.format("code=%d,raw=%s", resp.code, resp.rawBody));
    }


    public <T> UPN.UpnResponse<T> onParseJsonResponse(UPN.UpnResponse<T> resp, PtmNetOptions options) {
        resp = this.parseHttpCodeError(resp, options);
        try {
            resp.jsonBody = Utils.gson.fromJson(resp.rawBody, JsonObject.class);
        } catch (Exception e) {
            throw this.banker.jsonCastException(resp);
        }

        //--
        try {
            resp.jsonBody = Utils.gson.fromJson(resp.rawBody, JsonObject.class);
        } catch (Exception e) {
            throw this.banker.jsonCastException(resp);
        }
        //--
        try {
            resp.body = Utils.gson.fromJson(resp.jsonBody, options.respType);
        } catch (Exception e) {
            throw this.banker.jsonCastException(resp);
        }
        return resp;
    }

    @Override
    public UpckNetType netType() {
        return UpckNetType.PTM_HTTP;
    }

    @Override
    public OkHttpClient createOkClient() {
        return new OkHttpClient();
    }


    /**
     * 根据param key type获取对应的动态值
     * 实现对所有70个param key的统一处理
     *
     * @param paramKeyType PcpKT enum类型
     * @return String 对应的动态值，如果key不支持则抛出异常
     */
    public String getParamKey(PcPKT paramKeyType, PtmNetOptions options) {
        PtmDeviceUtils du = this.getDeviceUtils();
        FakeAndroid fad = du.getFAD();

        return switch (paramKeyType) {
            // 基础设备参数 (16个)
            case PLAY_STORE -> PtmDeviceUtils.playStore;                    // "true"
            case LANGUAGE -> du.getLang();                        // "en"
            case LOCALE -> du.getLocale();                        // "en-IN"
            case DEVICE_NAME -> du.getDeviceName();                // "Pixel_2"
            case VERSION -> PtmDeviceUtils.versionName;                    // "10.58.4"
            case DEVICE_IDENTIFIER, DEVICE_ID -> du.getDeviceIdentifier();    // "Google-Pixel2-1122554455bbffaa"
            case OS_VERSION -> fad.buildVersionRelease;                     // "11"
            case CLIENT -> PtmDeviceUtils.ptmClientApp; // 这个没错
            case DEVICE_MANUFACTURER -> fad.buildManufacturer;             // "Google"
            case NETWORK_TYPE -> PtmDeviceUtils.networkType;               // "WIFI"
            case APP_VERSION -> PtmDeviceUtils.versionName;                 // "10.58.4"
            case CHANNEL -> "app";                                         // "app"
            case CHANNEL_CODE -> "APP";                                     // "APP"
            case CHILD_SITE_ID -> PtmDeviceUtils.child_site_id;           // "1"
            case SITE_ID -> PtmDeviceUtils.site_id;                       // "1"
            case OPEN_SOURCE -> "false";                                    // "false"
            case OS -> "android";                                          // "android"

            // 位置相关参数 (5个)
            case LAT -> String.valueOf(fad.getFakeRealTimeLocation().latitude);   // "1.4328325"
            case LATITUDE -> String.valueOf(fad.getFakeRealTimeLocation().latitude);   // "1.4328325"
            case LONG -> String.valueOf(fad.getFakeRealTimeLocation().longitude); // "103.8333895"
            case LONGITUDE -> String.valueOf(fad.getFakeRealTimeLocation().longitude); // "103.8333895"
            case LOC_TIME -> "" + Math.floor((double) du.getLocTime() / 1000L);                 // "1750014644"
            // 语言和区域参数 (3个)
            case LANG_ID -> "1";                                           // "1"
            // 流程相关参数 (4个)
            case SESSION_ID -> null;                                        // null (需要从上下文获取)

            // 设备绑定相关参数 (3个)
            case DEB_CARRIER_NAME -> du.getDebCarrierName();
//            case IS_SMS_SENT_CHECK -> "false";                               // "false"
//            case IS_OUTBOX_CHECK -> "false";                                // "false"
            // 用户相关参数 (4个)
            case CUSTOMER_ID -> du.getPtmUserId();               // "**********"
            case MOBILE -> du.getPhoneNumber();                  // "**********"
            case FETCH_STRATEGY -> "full";                                // "full"

            // 分页相关参数 (6个)
            case TOP_PROVIDER_COUNT -> "10";                                // "10"
            case SHOW_PINNED_CHATS -> "true";                               // "true"

            // 时间相关参数 (4个)
            case TIMESTAMP -> String.valueOf(System.currentTimeMillis()); // "**********789"
            case REQUEST_TIMESTAMP -> String.valueOf(System.currentTimeMillis()); // "**********789"
            case TIMEZONE -> "Asia/Kolkata";                              // "Asia/Kolkata"
            case TXN_DATE -> String.valueOf(System.currentTimeMillis() / 1000);               // "**********"

            // 设备详细参数 (6个)
            case DEVICE -> fad.buildModel;                                // "Pixel 2"
            case RESOLUTION -> fad.pixelsWidth + "x" + fad.pixelsHeight;  // "1080x1920"
            case RISK_DEVICE_ID -> du.getDeviceIdentifier();                         // "1122554455bbffaa"
            case SIM_SUBSCRIPTION_ID -> du.getSubscriptionId();    // "4"

            // 功能开关参数 (12个)
            case CM_ID_INFO_REQUIRED -> "false";                             // "false"
            case FETCH_CREDIT_CARD_ACCOUNTS -> "true";                       // "true"
            case FETCH_INTERNATIONALISATION_DETAILS -> "true";              // "true"
            case FETCH_LRN_DETAILS -> "true";                               // "true"
            case FETCH_NEW_POLL_CONFIG -> "true";                            // "true"
            case FETCH_PREFERENCES -> "true";                              // "true"
            case FETCH_UPI_CREDIT_LINE_ACCOUNTS -> "true";                    // "true"
            case NON_DEFAULT_BANK_ACCOUNTS -> "true";                        // "true"
            case MODIFY_CTA_PLACEMENT -> "false";                           // "false"
            case MULTI_LANGUAGE -> "false";                                // "false"
            case PREFERENCE_KEYS -> "all";                                 // "all"
            case SERVICE -> "paytm";                                       // "paytm"

            // 版本相关参数 (4个)
            case PB_VERSION -> "1.0";                                       // "1.0"
            case PC_VERSION -> "1.0";                                       // "1.0"
            case PTH_VERSION -> "1.0";                                      // "1.0"
            case TYPE -> "mobile";                                         // "mobile"

            // 其他参数 (3个)
            case OFFER_TAG -> "default";                                   // "default"
            case THEME -> "light";                                         // "light"
            case TXN_ID -> String.valueOf(System.currentTimeMillis());     // "**********789"
            default -> throw new UpckException("UnknownParam:" + paramKeyType, UpckErrCode.DEV_FATAL);
        };
    }

    /**
     * 根据header key type获取对应的动态值
     * 实现对所有127个header key的统一处理
     *
     * @param headerKeyType PchKT enum类型
     * @return String 对应的动态值，如果key不支持则抛出异常
     */
    public String getHeaderKey(PcHKT headerKeyType, PtmNetOptions options) {
        PtmDeviceUtils du = this.getDeviceUtils();
        FakeAndroid fad = du.getFAD();
        var location = fad.getFakeRealTimeLocation();

        return switch (headerKeyType) {
            // 基础HTTP Headers (6个)
            case ACCEPT -> "application/json, text/plain, */*";           // "application/json, text/plain, */*"
            case ACCEPT_CHARSET -> "UTF-8";                               // "UTF-8"
            case ACCEPT_ENCODING -> "identity";                               // "gzip"
            case ACCEPT_LANGUAGE -> "en-US,en;q=0.9";                     // "en-US,en;q=0.9"


//            case CONTENT_TYPE -> "application/json";                      // "application/json"
//            case CONTENT_TYPE_LOWER -> "application/json";                // "application/json"
            // 认证相关Headers (6个)
//            case SSO_TOKEN -> du.getAccessToken();                  // "sso_token_12345"
//            case SSO_TOKEN_UNDERSCORE -> du.getAccessToken();       // "sso_token_12345"
//            case SSOTOKEN -> du.getAccessToken();                   // "sso_token_12345"
//            case SESSION_TOKEN -> du.getAccessToken();          // "session_abc123"
//            case SESSION_TOKEN_UNDERSCORE -> du.getAccessToken(); // "session_abc123"
//            case USER_TOKEN -> du.getAccessToken();                // "user_token_xyz789"


            // 设备相关Headers (12个)
            case X_DEVICE_IDENTIFIER -> du.getDeviceIdentifier(); // "Google-Pixel2-1122554455bbffaa"
            case DEVICE_IDENTIFIER -> du.getDeviceIdentifier(); // "Google-Pixel2-1122554455bbffaa"
            case X_DEVICE_MANUFACTURER -> fad.buildManufacturer;          // "Google"
            case X_DEVICE_NAME -> du.getDeviceName();            // "Pixel_2"
            case X_ID -> fad.androidId;                                    // "1122554455bbffaa"
            case X_MFG -> fad.buildManufacturer;                          // "Google"
            case X_MODEL -> fad.buildModel;                               // "Pixel 2"
            case X_SHORT_DEVICE_ID -> fad.androidId;                      // "1122554455bbffaa"
            case DEVICE_ID -> fad.androidId; //
            case DEVICE_ID_LOWER -> du.getDeviceIdentifier(); // "Google-Pixel2-1122554455bbffaa"
            case OS -> fad.buildVersionRelease;              // "11"
            case OS_VERSION -> fad.buildVersionRelease;              // "11"

            // 位置相关Headers (5个)
            case X_LATITUDE -> String.valueOf(location.latitude);         // "1.4328325"
            case X_LONGITUDE -> String.valueOf(location.longitude);       // "103.8333895"
            case X_LOC -> location.latitude + "," + location.longitude;   // "1.4328325,103.8333895"
            case X_LOCINT -> du.getXLocInt(); // "true,**********,true"
            case LAT_LNG -> location.latitude + "," + location.longitude; // "1.4328325,103.8333895"
            // 应用相关Headers (12个)
            case X_APP_RID -> du.getRid(options.currentTime);      // "1122554455bbffaa:**********789:1:123"
            case X_APP_VERSION -> PtmDeviceUtils.versionName;
            case APP_VERSION -> PtmDeviceUtils.versionName;
            case X_H5_VERSION -> du.getXH5Version();             //
            case AUTO_READ_HASH -> du.getAuthReadHash();           // "
            case X_EPOCH -> "" + options.currentTime / 1000L;    // "**********"
            case X_COUNTRY_CODE -> PtmDeviceUtils.countryCode;            // "IN"
            case CLIENT -> PtmDeviceUtils.ptmClientApp;                       // "androidapp"
            case APP_CHANNEL -> PtmDeviceUtils.appChannel;                                     // "APP"
            case SOURCE -> PtmDeviceUtils.appSource;//

            // 网络和状态Headers (10个)
            case X_NW -> PtmDeviceUtils.networkType;           // "WIFI"
            case NETWORK -> PtmDeviceUtils.networkType;           // "WIFI"
            case X_STORE -> PtmDeviceUtils.store;                                           // "1"
            case X_DEB_STATUS -> "" + PtmDeviceUtils.debStatus;                                  // "false"
            case X_LOCALE, LOCALE -> du.getLocale();           // "en-IN"
            case X_SIM_SUB_ID -> du.getSubscriptionId();         // "4"
            case X_PHONE_NUMBER -> du.getPhoneNumber();


            // 设备绑定相关Headers (12个)
            case X_SUBSCRIPTION_ID -> du.getSubscriptionId();
            case X_MULTI_PSP_GROUPID -> String.valueOf(System.currentTimeMillis() / 1000);


            // 用户相关Headers (12个)
            case USER_ID -> du.getPtmUserId();
            case USER_ID_CAMEL -> du.getPtmUserId();
            case X_USER_ID_UPPER -> du.getPtmUserId();
            case X_USER_ID -> du.getPtmUserId();
            case CUST_ID -> du.getPtmUserId();
//            case X_USER_TOKEN -> options.loginRequest ? null : du.getUserToken();
            case X_USER_TOKEN -> options.loginRequest ? null : du.getAccessToken();
            case X_CLIENT_ID -> PtmDeviceUtils.ptmXClientId;
            case CLIENT_ID -> PtmDeviceUtils.ptmClientId;

            //--有误修正的
            case X_CALL -> "0";
            case X_INTG -> "1";
            case X_TAMP -> "0";
            case X_CLON -> "0";
            case X_INTG_SRC -> "0";

            // 支付相关Headers (22个)
            case ORDER_ID -> String.valueOf(System.currentTimeMillis());
            case MID -> du.getMerchantId();
            case MKTPLACE_APIKEY -> PtmDeviceUtils.mktPlaceApiKey;
            case CHANNEL_TOKEN -> du.getAccessToken();
            case TIMESTAMP -> String.valueOf(System.currentTimeMillis());
            case HASH -> du.generateHash();
            case X_DFP -> du.getDeviceFingerprint();
            case ENTRY_POINT -> PtmDeviceUtils.channel;
            case IP -> du.getFakeIp();
            case RETRY_COUNT -> "0";
            case USER_STAGE -> "VERIFIED";
            case X_REQUESTER -> "mobile_app";
            case VERTICAL_NAME -> "TPAP_UPI";
            case X_FORCE_DEB -> "false";
            case X_MULTI_PSP_PRIMARY -> "ptyes";
            case X_PSP_NAME -> "PAYTM";
            case X_GET_SERVER_RESPONSE_TIME -> "true";
            case OTP_AUTOREAD -> "true";
            case X_DEV_INTEGRITY_INT -> "FAIL";
            case X_DEV_INTEGRITY_LICENSED -> "NO_RISK";
            case X_DEV_INTEGRITY_TEMP -> "NO_RISK";
            case X_AUTH_UMP -> "true";
            case X_CONSENT_PSEUDO -> "true";
            case UPI_LITE_REGISTERED -> "false";
            case UPI_LITE_SUPPORTED -> "false";
            case UPI_ORDER_DETAILS -> "{}";
            case ULTRA_MODE -> "false";
            case MODALITY -> "UPI";
            case PULSE_CATEGORY -> "DEFAULT";
            case API_ROLE -> "CUSTOMER";
            case INSTRUMENT_TYPE -> "UPI";
            case DATA -> "{}";
            case ENABLE_BOTF -> "false";
            case LAST_PAYMENT_DETAILS -> "{}";
            case OPEN_SOURCE -> "false";
            default -> throw new UpckException("UnknownHeader:" + headerKeyType, UpckErrCode.DEV_FATAL);
        };
    }


}
