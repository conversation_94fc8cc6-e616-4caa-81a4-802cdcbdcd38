package fpi.upck.banks.in.paytm.models.oauth2;

import fpi.upck.banks.in.paytm.models.BaseAccountResp;

/**
 * PostOauth2V2AuthorizeSv1Resp - OAuth2 V2 authorize response - 修正版本
 * Generated from HAR file data
 * URL: https://accounts.paytm.com/oauth2/v2/authorize/sv1
 * <p>
 * 实际响应结构:
 * {
 * "status": "SUCCESS",
 * "responseCode": "BE1400001",
 * "message": "SUCCESS",
 * "data": {
 * "code": "9e42dcdd-752c-460a-8aab-3be3a92e2400"
 * }
 * }
 */
public class Oauth2V2AuthorizeSv1Resp extends BaseAccountResp<Oauth2V2AuthorizeSv1Resp.Data> {
    public static class Data {
        /**
         * 授权码
         */
        public String code;
    }
}
