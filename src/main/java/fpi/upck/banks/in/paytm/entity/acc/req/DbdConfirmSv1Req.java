package fpi.upck.banks.in.paytm.entity.acc.req;

/**
 * DevicebindingConfirmSv1Req - Request class for /devicebinding/confirm/sv1
 * Generated from HAR file data
 *
 * Request body example:
 * {
  "sessionId": "$MWEZNJHHZMJMNJRMZTAXYZQ1NDE1MZKYMDI0MGMZNJQ",
  "method": "otp",
  "meta": {
    "phone": "**********",
    "otp": "123456"
  }
}
 */
public class DbdConfirmSv1Req {

    public String sessionId;
    public String method;
    public String initTimeEpoch;
    public Meta meta;

    /**
     * Meta class for nested meta object
     */
    public static class Meta {
        public String phone;
        public String otpValue;

        public Meta() {}

        public Meta(String phone, String otp) {
            this.phone = phone;
            this.otpValue = otp;
        }
    }

    /**
     * Default constructor
     */
    public DbdConfirmSv1Req() {
    }

    /**
     * Constructor with all parameters
     */
    public DbdConfirmSv1Req(String sessionId, String method, Meta meta) {
        this.sessionId = sessionId;
        this.method = method;
        this.meta = meta;
    }
}