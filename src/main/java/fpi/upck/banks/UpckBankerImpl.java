package fpi.upck.banks;

import com.google.gson.reflect.TypeToken;
import com.linecorp.armeria.client.WebClient;
import com.linecorp.armeria.client.WebClientBuilder;
import fpi.upck.banks.common.config.IAccountConfig;
import fpi.upck.banks.common.config.IBankerConfig;
import fpi.upck.banks.common.engine.caches.UKV;
import fpi.upck.banks.common.engine.caches.UvItem;
import fpi.upck.banks.common.engine.loggers.IUpckLogger;
import fpi.upck.banks.common.engine.loggers.UpckBankerLogger;
import fpi.upck.banks.common.engine.manager.IUpckDeviceUtils;
import fpi.upck.banks.common.engine.net.UPN;
import fpi.upck.banks.common.engine.net.UpbBankerNet;
import fpi.upck.banks.common.engine.net.UpnCookie;
import fpi.upck.banks.common.engine.risk.VDT;
import fpi.upck.banks.common.engine.risk.ValidateManager;
import fpi.upck.banks.common.engine.upi.QrValidate;
import fpi.upck.banks.common.reflect.JoorReflect;
import fpi.upck.common.UpckBanker;
import fpi.upck.common.engine.UpckLogOptions;
import fpi.upck.common.entity.FICK;
import fpi.upck.common.entity.UGB;
import fpi.upck.common.entity.account.FpiAccount;
import fpi.upck.common.entity.auth.INPUT;
import fpi.upck.common.entity.auth.UpckCredAllow;
import fpi.upck.common.entity.auth.UpckTokenStatus;
import fpi.upck.common.entity.bank.UpckBankType;
import fpi.upck.common.entity.bank.UpiCountryType;
import fpi.upck.common.entity.firebase.InstallTokenResp;
import fpi.upck.common.entity.functions.UpckFunctionType;
import fpi.upck.common.entity.card.UpckCardStatus;
import fpi.upck.common.entity.device.FakeAndroid;
import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;
import fpi.upck.common.entity.network.UpckNetType;
import fpi.upck.common.entity.payment.*;
import fpi.upck.common.entity.payment.UpckPayToTask.Direction;
import fpi.upck.common.entity.proxy.IpInfo;
import fpi.upck.common.entity.psp.PspStatus;
import fpi.upck.common.entity.psp.VdtEchoData;
import fpi.upck.common.entity.psp.VpaDetails;
import fpi.upck.common.entity.transactions.QueryTxnItem;
import fpi.upck.common.entity.transactions.UpckQueryOptions;
import fpi.upck.common.entity.transactions.UqdOptions;
import fpi.upck.utils.Utils;
import lombok.SneakyThrows;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.client5.http.impl.async.HttpAsyncClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.asynchttpclient.DefaultAsyncHttpClientConfig;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static fpi.upck.common.entity.exceptions.UpckErrCode.DEV_FATAL;


/**
 * 这类全局核心
 */
public abstract class UpckBankerImpl implements UpckBanker,
        IAccountConfig,
        IBankerConfig,
        IUpckLogger {

    public abstract UpckBankType bankType();


    protected abstract List<UpbBankerNet> listOfChannels();


    protected abstract List<String> listOfBaseUrls();

    private List<String> listOfHosts() {
        ArrayList<String> baseUrls = new ArrayList<>(this.listOfBaseUrls());
        baseUrls.addAll(List.of("https://ipinfo.io",
                "http://firebaseinstallations.googleapis.com"));

        return baseUrls.stream().map(url -> Objects.requireNonNull(HttpUrl.parse(url)).host())
                .collect(Collectors.toList());
    }


    public abstract FICK.SESSION.LOGIN onLoginPrepare(INPUT.LOGIN.PREPARE input, FICK.SESSION.LOGIN previous);

    public abstract FICK.AUTH.LOGIN onLoginConfirm(INPUT.LOGIN.CONFIRM input, FICK.SESSION.LOGIN loginSession);

    public abstract FICK.SESSION.KYC onKycPrepare(INPUT.KYC.PREPARE prepareInput, FICK.SESSION.KYC previous);

    public abstract FICK.AUTH.KYC onKycConfirm(INPUT.KYC.CONFIRM confirmInput, FICK.SESSION.KYC session, List<String> targetOperators);

    public abstract FICK.SESSION.PAY onPayPrepare(List<UpckPayToTask> payTos, UpckPayOptions payOptions);

    public abstract void onPayConfirm(List<UpckPayToTask> payTos, UpckPayOptions payOptions, FICK.SESSION.PAY session);


    public abstract <T extends FICK.DEVICE> Class<T> classDeviceStorage();

    public abstract <T extends FICK.AUTH.LOGIN> Class<T> classLoginAuth();

    public abstract <T extends FICK.SESSION.LOGIN> Class<T> classLoginSession();

    public abstract <T extends FICK.AUTH.KYC> Class<T> classKycAuth();

    public abstract <T extends FICK.SESSION.KYC> Class<T> classKycSession();

    public abstract <T extends FICK.SESSION.PAY> Class<T> classPaySession();

    public abstract <T extends FICK.SESSION.PIN> Class<T> classPinSession();

    public abstract <T, D extends FICK.ACCOUNT.UPI<T>> Class<D> classUpiDetails();

    public abstract <T, D extends FICK.ACCOUNT.CARD<T>> Class<D> classCard();

    public abstract <T, D extends FICK.BANK<T>> Class<D> classBank();

    protected abstract <T, F extends FICK.ACCOUNT.CARD<T>, D extends List<F>> D onConvertCardList(Object respCards);


    /**
     * 转换upi账户结构体,这个函数体内不做card的信息处理,不然极有可能死循环
     *
     * @param respData
     * @param <T>
     * @return
     */
    protected abstract <T, D extends FICK.ACCOUNT.UPI<T>> D onConvertUpiDetails(Object respData);

    protected abstract Object onRequestFetchCards();

    protected abstract boolean onCardLinkToVpa(String cardId, String selfVpa);


    protected abstract Object onRequestUpiDetails();


    protected abstract <T, D extends FICK.BANK<T>> List<D> onQueryBankList();

    protected abstract boolean onCardAdd(FICK.BANK bankInfo, String selfVpa);

    protected abstract boolean onCardDelete(FICK.ACCOUNT.CARD card, String selfVpa);

    protected abstract List<VDT> extraVdtTasks();

    protected abstract boolean onVpaCreate(String vpa, String cardId);

    protected abstract FICK.SESSION.PIN onCardSetPinPrepare(FICK.ACCOUNT.CARD card, VpaDetails vpaDetails, INPUT.PIN.SET.PREPARE input);

    protected abstract boolean onCardSetPinConfirm(INPUT.PIN.SET.CONFIRM input, FICK.SESSION.PIN session);

    public abstract <T extends RT> T getRT();

    public static class RT {
        protected UpckBankerImpl banker;
        protected Set<UKV> fieldKeys = new HashSet<>();
        public UKV.LOCK<FICK.AUTH.LOGIN> refreshLogin;
        public UKV.LOCK<FICK.AUTH.KYC> refreshKycData;
        public UKV.LOCK<InstallTokenResp> firebaseInstallToken;

        public UKV.COLLECTION<UpnCookie> rtCookies;


        public RT(UpckBankerImpl banker) {
            this.banker = banker;
            this.refreshLogin = this.add((new UKV.LOCK<FICK.AUTH.LOGIN>("refreshLogin", this)
                    .disableCache()
                    .lockTime(1000 * 60 * 5)//
                    .expired(1000 * 60 * 60 * 30))
                    .func(() -> this.getBanker().onRefreshLoginToken())
                    .typeOf(this.banker.classLoginAuth())
            );
            this.refreshKycData = this.add((new UKV.LOCK<FICK.AUTH.KYC>("refresh_kyc_data_v2", this) //登录otp需要使用的密钥key
                    .lockTime(1000 * 60 * 5)//
                    .expired(1000 * 60 * 60 * 2))//存两个小时
                    .func(() -> this.getBanker().refreshKycToken())
                    .typeOf(this.banker.classKycAuth())
            );

            this.firebaseInstallToken = this.add((new UKV.LOCK<InstallTokenResp>("firebase_install_token_v0", this) //登录otp需要使用的密钥key
                    .lockTime(1000 * 60 * 5)//
                    .func(() -> this.getBanker().refreshFirebaseInstallToken())
                    .expired(resp -> this.getBanker().firebaseExpired(resp))
                    .typeOf(InstallTokenResp.class)
            ));

            this.rtCookies = this.add(
                    new UKV.COLLECTION<UpnCookie>("cookies_v0", this)
                            .expiredItem(cc ->
                                    cc.maxAge == -1 ? Integer.MAX_VALUE : cc.maxAge * 1000)
                            .collision((v0, v1) ->
                                    //过滤一下冲突
                                    v0.value.whenCreated > v1.value.whenCreated ? v0 : v1)
                            .expired(1000L * 60 * 60 * 24 * 15)
                            .typeOf(new TypeToken<List<UvItem<UpnCookie>>>() {
                            }.getType()))
            ;
        }

        public <T, D extends UKV<T>> D add(D input) {
            this.fieldKeys.add(input);
            return input;
        }

        public void clear() {
            for (UKV ukv : this.fieldKeys) {
                ukv.clear();
            }
        }

        public <T extends UpckBankerImpl> T getBanker() {
            return (T) this.banker;
        }

    }

    private InternalCallback uac;

    public String accountId;
    protected IUpckDeviceUtils deviceUtils;
    protected RT runtimeParams;


    private FICK.DEVICE deviceStorage;
    private FICK.AUTH.LOGIN authLoginData;
    private FICK.AUTH.KYC authKycData;
    private FpiAccount account;

    //--
    private Object upiDetailResp;
    private FICK.ACCOUNT.UPI<Object> upiDetailFickCache;
    private Object cardResp;
    private Map<String, FICK.ACCOUNT.CARD<Object>> cardsFickCache;

    //--
    private UpckBankerLogger loggerDefault;
    private UpckBankerLogger loggerRecord;
    private UpckBankerLogger loggerConsole;

    public UpckBankerImpl(InternalCallback appContext, String accountId) {
        this.accountId = accountId;
        this.uac = appContext;

        this.loggerDefault = new UpckBankerLogger(this, UpckLogOptions.EchoType.DEFAULT);
        this.loggerRecord = new UpckBankerLogger(this, UpckLogOptions.EchoType.RECORD);
        this.loggerConsole = new UpckBankerLogger(this, UpckLogOptions.EchoType.CONSOLE);


    }

    public InternalCallback getAppCtx() {
        return this.uac;
    }


    public FpiAccount getFpiAccount() {
        if (this.account != null) return this.account;
        this.account = this.getAppCtx().onGetFpiAccount(this.accountId);
        return this.account;
    }

    public List<String> trimPathOfUpiStatusData() {
        return new ArrayList<>();
    }

    public List<String> trimPathOfCardData() {
        return new ArrayList<>();
    }

    public boolean runCardLinkToVpa(String cardId, String selfVpa) {
        FICK.ACCOUNT.CARD targetCard = this.fetchCardById(cardId);
        VpaDetails vpaDetails = this.fetchVpaDetails(selfVpa);
        if (targetCard == null) {
            throw UpckErrCode.ERR_CARD_NOT_FOUND();
        }
        if (targetCard.status == UpckCardStatus.UNLINK) {
            throw UpckErrCode.ERR_CARD_UNLINK();
        }
        if (vpaDetails == null) {
            throw UpckErrCode.ERR_SELF_VPA_NOT_FOUND();
        }
        if (cardId.equals(vpaDetails.specifyCardId) &&
                (vpaDetails.status == PspStatus.ACTIVATED || vpaDetails.status == PspStatus.UNKNOWN)
        ) {
            this.logW("Already Bind");
            return true;
        }
        if (!Utils.isEmpty(targetCard.specifyVpa)
                && targetCard.specifyVpa.equals(selfVpa)) {
            this.logW("Already Bind");
            return true;
        }
        return this.onCardLinkToVpa(cardId, selfVpa);
    }


    public FICK.AUTH.LOGIN onRefreshLoginToken() {
        throw new UpckException("Not Support", UpckErrCode.DEV_NOT_SUPPORT);
    }

    public FICK.AUTH.KYC onRefreshKycToken() {
        throw new UpckException("Not Support", UpckErrCode.DEV_NOT_SUPPORT);
    }

    public FICK.AUTH.KYC refreshKycToken() {
        FpiAccount fpiAccount = this.getFpiAccount();
//        if (fpiAccount.ecMock) {
//            throw new UpckException("LocalDemo Not Support Refresh KycToken", DEV_FATAL);
//        }
        FICK.AUTH.KYC kycData = this.onRefreshKycToken();
        this.authKycData = kycData;
        return kycData;
    }

    public InstallTokenResp refreshFirebaseInstallToken() {
        UpbBankerNet netChannel = Utils.getRandomElement(this.listOfChannels());
        assert netChannel != null;
        return netChannel.firebaseInstallToken();
    }

    public long firebaseExpired(InstallTokenResp resp) {
        return Utils.parseExpToMillis(resp.authToken.expiresIn) - 1000 * 10L;
    }


    protected UGB.AUTH.LOGIN runRefreshLoginToken() {
        FICK.AUTH.LOGIN loginFick = this.refreshLoginToken();
        return new UGB.AUTH.LOGIN(loginFick);
    }

    protected UGB.AUTH.LOGIN notifyRefreshLoginAuthData(FICK.AUTH.LOGIN authLoginData) {

        UGB.AUTH.LOGIN ugb = new UGB.AUTH.LOGIN(authLoginData);
        this.getAppCtx().onRefreshLoginAuthData(this.accountId, ugb);
        return ugb;
    }


    private FICK.DEVICE readDeviceStorage() {
        UGB.DEVICE deviceUGB = this.getAppCtx().onGetDeviceStorage(this.accountId);
        if (deviceUGB == null)
            throw UpckErrCode.ERR_DEV_FATAL("Device Data Empty");

        return FICK.fromUGB(deviceUGB, this.classDeviceStorage());
    }

    public FICK.DEVICE getDeviceStorage() {
        if (this.deviceStorage == null) this.deviceStorage = this.readDeviceStorage();
        return this.deviceStorage;
    }

    protected FICK.AUTH.LOGIN readAuthLoginData() {
        UGB.AUTH.LOGIN loginDGB = this.getAppCtx().onGetAuthLoginData(this.accountId);
        if (loginDGB == null)
            throw UpckErrCode.ERR_DEV_FATAL("Login Auth Data Empty");

        return FICK.fromUGB(loginDGB, this.classLoginAuth());
    }

    public FICK.AUTH.LOGIN getAuthLoginData() {
        if (this.authLoginData != null) return this.authLoginData;
        this.authLoginData = this.readAuthLoginData();
        if (this.authLoginData == null) {
            //这里还取不到,一定是哪里有问题了
            throw new UpckException("AuthLoginData Null", DEV_FATAL);
        }
        boolean isExpired = this.checkLoginAuthExpired(this.authLoginData);
        if (!isExpired) return this.authLoginData;
        this.logW("Login Auth Expired,Refresh now");
        this.authLoginData = this.refreshLoginToken();
        return this.authLoginData;
    }


    public FICK.AUTH.LOGIN refreshLoginToken() {
        if (this.getFpiAccount().ecMock)
            throw new UpckException("LocalDemo Not Support Refresh Token", UpckErrCode.DEV_VALIDATE);

        this.authLoginData = this.getRT().refreshLogin.get(true);
        if (this.authLoginData == null) throw new UpckException("Refresh Token Null", UpckErrCode.DEV_UNEXPECT);
        this.notifyRefreshLoginAuthData(this.authLoginData);
        return this.authLoginData;
    }

    public boolean checkLoginAuthExpired(FICK.AUTH.LOGIN fickData) {
        return false;
    }

    public UpckTokenStatus runCheckAuthLoginEnable() {
        return UpckTokenStatus.UNKNOWN;
    }


    public UpckTokenStatus checkAuthKycEnable(UGB.ACCOUNT.UPI ugbUpi, String selfVpa) {
        FICK.ACCOUNT.UPI<Object> fick = FICK.convertRawToFICK(ugbUpi, this.classUpiDetails());
        return this.checkAuthKycEnable(fick, selfVpa);
    }

    public <T> UpckTokenStatus checkAuthKycEnable(FICK.ACCOUNT.UPI<T> upiDetails, String selfVpa) {
        if (Boolean.TRUE.equals(upiDetails.definitelyRegistered)) {
            return UpckTokenStatus.ENABLE;
        }
        if (Boolean.TRUE.equals(upiDetails.deviceChanged)) {
            return UpckTokenStatus.DISABLE;
        }
        if (Boolean.FALSE.equals(upiDetails.previousRegistered)) {
            return UpckTokenStatus.DISABLE;
        }
        if (Boolean.TRUE.equals(upiDetails.definitelyUnRegistered)) {
            return UpckTokenStatus.DISABLE;
        }
        return this.onCheckAuthKycEnable(upiDetails, selfVpa);
    }


    public <T> UpckTokenStatus onCheckAuthKycEnable(FICK.ACCOUNT.UPI<T> upiDetails, String selfVpa) {
        return UpckTokenStatus.UNKNOWN;
    }


    public FICK.AUTH.KYC getAuthKycData() {
        if (this.authKycData != null) return this.authKycData;
        this.authKycData = this.readKycFromByRT();
        return this.authKycData;
    }

    private FICK.AUTH.KYC readKycFromByRT() {
        return this.getRT().refreshKycData.get();
    }

    public UGB.AUTH.KYC runRefreshKycToken() {
        FICK.AUTH.KYC authFick = this.refreshKycToken();
        return new UGB.AUTH.KYC(authFick);
    }

    public VpaDetails findPrimaryVpa() {
        return this.findPrimaryVpa(null);
    }

    public <T> VpaDetails findPrimaryVpa(FICK.ACCOUNT.UPI<T> upiDetails) {
        if (upiDetails == null) upiDetails = this.fetchUpiDetails();

        if (Utils.isEmpty(upiDetails.vpas)) return null;
        VpaDetails vpaDetails = upiDetails.vpas.values().stream()
                .filter(v -> v.primary)
                .findAny()
                .orElse(null);
        if (vpaDetails == null) return null;
        return vpaDetails;
    }


    public UpckException throwUnknownError(String msg) throws UpckException {
        UpckException error = new UpckException(msg, UpckErrCode.UNKNOWN);
        this.logConsoleE(error.getUnknownMessage());
        return error;
    }


    private <T extends FICK.DEVICE> T createNewDevice() {
        return JoorReflect.on(this.classDeviceStorage()).create().get();
    }

    @Override
    public UGB.DEVICE createFakedDevice(FakeAndroid inputParams) {
        this.getRT().clear();   //清除所有的缓存临时变量
        this.logV("Create Fake Device");
        FakeAndroid fad = FakeAndroid.createFakeDevice(UpiCountryType.IN);
        FICK.DEVICE ds = this.deviceUtils.onCreateFakedDevice(fad);
        this.logV("Merge Android Device Params");
        ds.fad = Utils.merge(fad, inputParams);
        return new UGB.DEVICE(ds);
    }


    public UGB.SESSION.LOGIN runAuthorizedByLoginPrepare(INPUT.LOGIN.PREPARE prepareInput, UGB.SESSION.LOGIN previous) {
        FICK.SESSION.LOGIN previousFick = null;
        if (previous != null) {
            previousFick = FICK.fromUGB(previous, this.classLoginSession());
        } else {
            //清除所有的缓存临时变量
            this.getRT().clear();
        }

        //
        FICK.SESSION.LOGIN genericData = this.onLoginPrepare(prepareInput, previousFick);
        return new UGB.SESSION.LOGIN(genericData);
    }


    @SneakyThrows
    public final Map<UpckNetType, OkHttpClient> createOkClients() {
        return this.listOfChannels().stream()
                .collect(Collectors.toMap(
                        UpbBankerNet::netType,
                        UpbBankerNet::createOkClient
                ));
    }

    public final Map<UpckNetType, HttpClientBuilder> createApacheHttpClients() {
        return this.listOfChannels().stream()
                .collect(Collectors.toMap(
                        UpbBankerNet::netType,
                        UpbBankerNet::createApacheHttpClientBuilder
                ));
    }

    public final Map<UpckNetType, DefaultAsyncHttpClientConfig.Builder> createAhcBuilders() {
        return this.listOfChannels().stream()
                .collect(Collectors.toMap(
                        UpbBankerNet::netType,
                        UpbBankerNet::createAhcBuilder
                ));
    }

    public final Map<String, WebClientBuilder> createArmeriaBuilder() {
        return this.listOfHosts().stream().collect(Collectors.toMap(k -> k,
                k -> WebClient.builder("https://" + k)
        ));
    }


    public final Map<UpckNetType, HttpAsyncClientBuilder> createApacheAsyncHttpClients() {
        return this.listOfChannels().stream()
                .collect(Collectors.toMap(
                        UpbBankerNet::netType,
                        UpbBankerNet::createApacheHttpAsyncClientBuilder
                ));
    }


    public UGB.AUTH.LOGIN runAuthorizedByLoginConfirm(INPUT.LOGIN.CONFIRM input, UGB.SESSION.LOGIN session) {
        FICK.SESSION.LOGIN sessionLoginFick = FICK.fromUGB(session, this.classLoginSession());
        FICK.AUTH.LOGIN authDataFick = this.onLoginConfirm(input, sessionLoginFick);
//        System.out.println("LOGIN:"+Utils.gson.toJson(authDataFick));
        return new UGB.AUTH.LOGIN(authDataFick);

    }

    protected UGB.SESSION.KYC skipSmsRegisteredSession() {
        FICK.SESSION.KYC fickData = new FICK.SESSION.KYC();
        fickData.current = FICK.STEP.REGISTERED;
        fickData.next = FICK.STEP.REGISTERED;
        fickData.authData = this.runRefreshKycToken();
        return new UGB.SESSION.KYC(fickData);
    }

    public boolean disableKycRefreshUpiToken() {
        return false;
    }


    public UGB.SESSION.KYC runAuthorizedByKycPrepare(INPUT.KYC.PREPARE prepareInput, UGB.SESSION.KYC previous) {
        //
        this.precheckKycPrepareInput(prepareInput);
        FICK.SESSION.KYC previousFick = null;
        if (previous != null) {
            previousFick = FICK.fromUGB(previous, this.classKycSession());
        } else {
            //--检测一下是否可用
            FICK.ACCOUNT.UPI upiDetails = this.fetchUpiDetails();
            UpckTokenStatus tokenStatus = this.checkAuthKycEnable(upiDetails, prepareInput.selfVpa);
            if (tokenStatus == UpckTokenStatus.ENABLE) {
                return this.skipSmsRegisteredSession();
            }
        }
        //--
        FICK.SESSION.KYC fickData = this.onKycPrepare(prepareInput, previousFick);
        return new UGB.SESSION.KYC(fickData);
    }

    public void precheckKycPrepareInput(INPUT.KYC.PREPARE smsInput) throws UpckException {
        //这里强制验证输入内容
//        if (Utils.isEmpty(smsInput.selfVpa)) {
//            throw new UpckException("Sms Auth Prepare Input Vpa Params Null ", UpckErrCode.DEV_VALIDATE);
//        }
    }


    public UGB.AUTH.KYC runAuthorizedByKycConfirm(INPUT.KYC.CONFIRM confirmInput, UGB.SESSION.KYC session) {
        if (session.next != FICK.STEP.FINAL_CONFIRM) {
            throw new UpckException("Sms Confirm Set Must be FINAL_CONFIRM", DEV_FATAL);
        }
        List<String> targetOperators = Utils.isEmpty(confirmInput.filterOperators) ?
                session.mobileOperators : confirmInput.filterOperators;
        FICK.SESSION.KYC sessionFick = FICK.fromUGB(session, this.classKycSession());
        FICK.AUTH.KYC authFick = this.onKycConfirm(confirmInput, sessionFick, targetOperators);
        return new UGB.AUTH.KYC(authFick);
    }

    public <T> void checkCardCred(FICK.ACCOUNT.CARD<T> cardV2, String pwd) throws UpckException {
        if (this.optionsOfSkipCheckCardCred()) return;
        this.checkCardCred(cardV2, pwd, UpckCredAllow.SubType.MPIN);
    }

    public boolean optionsOfSkipCheckCardCred() {
        return false;
    }


    public <T> void checkCardCred(FICK.ACCOUNT.CARD<T> cardV2, String pwd, UpckCredAllow.SubType subType) throws UpckException {
        UpckCredAllow credAllow = cardV2.credAlloweds.get(UpckCredAllow.SubType.MPIN);
        if (credAllow == null) {
            throw new UpckException("Card Not Supported MPIN Mode:" + Utils.gson.toJson(cardV2.orig), UpckErrCode.DEV_UNEXPECT);
        }
        if (credAllow.dLen == null) {
            this.logW("CredAllow Null");
            return;
        }
        if (pwd.length() != credAllow.dLen) {
            throw UpckErrCode.ERR_CARD_PIN_LEN_WRONG(pwd.length(), credAllow.dLen);
        }

    }

    public String getAccountPassword() throws UpckException {
        FpiAccount account = this.getFpiAccount();
        if (Utils.isEmpty(account.password)) {
            throw UpckErrCode.ERR_PIN_PWD_LACKED();
        }
        return account.password;
    }


    @Override
    public UGB.SESSION.PAY runPayTasksPrepare(List<UpckPayToTask> payTos, UpckPayOptions payOptions) {
        String selfVpa = payOptions.selfVpa;
        String cardId = payOptions.selfCardId;
        if (payOptions.upiDetails != null) {
            /*
            cache一下外部传进来的,省得再去找外面请求
             */
            this.upiDetailFickCache = FICK.convertRawToFICK(payOptions.upiDetails, this.classUpiDetails());
        }
        if (payOptions.cards != null) {
               /*
            cache一下外部传进来的,省得再去找外面请求
             */
            this.cardsFickCache = this.convertCardsF2U(payOptions.cards);
        }


        /**
         * 验证单子问题
         */
        if (payTos.stream().anyMatch(p -> Utils.isEmpty(p.id)))
            throw new UpckException("PayIdNull", DEV_FATAL);


        /**
         * 判断qr code内容是否正确
         */
        QrValidate qrValidate = payTos.stream().filter(p -> p.channel == UpckPayChannel.QR_CODE)
                .map(p -> this.validateQr(p.payQR))
                .filter(Objects::nonNull)
                .findAny().orElse(null);
        if (qrValidate != null) {
            throw new UpckException(qrValidate.errorMsg, qrValidate.errorCode);
        }
        //--
        if (Utils.isEmpty(selfVpa)) {
            throw new UpckException("PayOption Need selfVpa", DEV_FATAL);
        }
        if (Utils.isEmpty(cardId)) {
            throw new UpckException("PayOption Need cardId", DEV_FATAL);
        }


        /**
         * 检测账户状态
         */
        FICK.ACCOUNT.UPI<?> upiDetails = this.fetchUpiDetails();
        if (Boolean.FALSE.equals(upiDetails.previousRegistered)) {
            throw new UpckException("Device Not Registered", UpckErrCode.KYC_DEVICE_NOT_REGISTERED);
        }

        if (Boolean.TRUE.equals(upiDetails.deviceChanged)) {
            throw new UpckException("Device Changed", UpckErrCode.KYC_DEVICE_CHANGED);
        }

        /**
         * 检测卡状态
         */
        FICK.ACCOUNT.CARD<?> selfCard = this.fetchCardById(cardId);
        if (selfCard == null) {
            throw UpckErrCode.ERR_CARD_NOT_FOUND();
        }

        if (selfCard.status == UpckCardStatus.UNLINK) {
            throw UpckErrCode.ERR_CARD_UNLINK();
        }

        //
        if (Boolean.FALSE.equals(selfCard.mbeba)) {
            throw new UpckException("Card MoneyOut Disable", UpckErrCode.CARD_OUT_DISABLE);
        }
        if (!Boolean.TRUE.equals(selfCard.primary)) {
            this.logW("Card is not primary");
        }
        if (!Utils.isEmpty(payOptions.password)) {
            this.checkCardCred(selfCard, payOptions.password);
        }
        if (selfCard.maxLimitPerTxn != null) {
            UpckPayToTask errPayTo = payTos.stream()
                    .filter(p -> selfCard.maxLimitPerTxn.compareTo(p.amount) < 0)
                    .findAny()
                    .orElse(null);
            if (errPayTo != null) {
                throw new UpckException("Exceed Max Limit Amount:" + selfCard.maxLimitPerTxn, UpckErrCode.CARD_MAX_AMOUNT_PER_PAY);
            }
        }


        //--vpa
        VpaDetails vpaDetails = this.fetchVpaDetails(selfVpa);
        if (vpaDetails == null) {
            throw new UpckException("Vpa Not Found:" + selfVpa, UpckErrCode.VPA_NOT_FOUND);
        }
        if (vpaDetails.status == PspStatus.DELETED) {
            throw new UpckException("Vpa Deleted Status:" + selfVpa, UpckErrCode.VPA_DELETED);
        }

        //
        /**
         if (Utils.isEmpty(cardV2.vpas)) {
         throw new UpckException("Card not attach any vpas")
         .code(UpckErrCode.CARD_NOT_MATCH_VPA);
         }
         if (cardV2.vpas.contains(selfVpa)) {

         }
         */

        FICK.AUTH.KYC authKycData = this.getAuthKycData();
        if (authKycData == null) {
            throw new UpckException("Kyc Data Empty", DEV_FATAL);
        }
        if (payOptions.bank != null)
            payOptions.fickBank = FICK.convertRawToFICK(payOptions.bank, this.classBank());
        FICK.SESSION.PAY sessionFick = this.onPayPrepare(payTos, payOptions);
        sessionFick.vpaDetails = vpaDetails;
        UGB.SESSION.PAY ugb = new UGB.SESSION.PAY(sessionFick);
        ugb.selfCard = FICK.convertRawToUGB(selfCard, UGB.ACCOUNT.CARD.class);
        ugb.upiDetails = FICK.convertRawToUGB(upiDetails, UGB.ACCOUNT.UPI.class);
        return ugb;
    }


    @Override
    public void runPayTasksConfirm(List<UpckPayToTask> payTos, UpckPayOptions payOptions, UGB.SESSION.PAY session) {
        if (Utils.isEmpty(payOptions.password)) {
            throw new UpckException("Password Empty", DEV_FATAL);
        }
        if (payOptions.upiDetails != null) {
            /*
            cache一下外部传进来的,省得再去找外面请求
             */
            this.upiDetailFickCache = FICK.convertRawToFICK(payOptions.upiDetails, this.classUpiDetails());
        }
        if (payOptions.cards != null) {
               /*
            cache一下外部传进来的,省得再去找外面请求
             */
            this.cardsFickCache = this.convertCardsF2U(payOptions.cards);
        }

        FICK.SESSION.PAY sessionFick = FICK.fromUGB(session, this.classPaySession());
        if (sessionFick == null) throw new UpckException("SessionNull", DEV_FATAL);
        sessionFick.selfCard = FICK.convertRawToFICK(session.selfCard, this.classCard());
        sessionFick.upiDetails = FICK.convertRawToFICK(session.upiDetails, this.classUpiDetails());
        //默认采用mpin模式
        this.checkCardCred(sessionFick.selfCard, payOptions.password);
        this.onPayConfirm(payTos, payOptions, sessionFick);
    }


    public UpckBeneInfo runQueryBeneInfo(QueryBeneOptions options) {
        throw new UpckException("runQueryBeneVpa must override", UpckErrCode.DEV_NOT_SUPPORT);
    }


    /**
     * 查询一单的详情
     *
     * @param options 查询参数
     * @return
     */
    public QueryTxnItem runQueryOneTxnDetails(UqdOptions options) {
        return this.onQueryOneTxnDetails(options).join();
    }

    /**
     * @param nextQuery 由上次查询输入进来的
     * @return
     */
    public QueryTxnItem runQueryOneTxnDetails(String nextQuery) {
        return this.onQueryOneTxnDetails(nextQuery).join();
    }

    public CompletableFuture<QueryTxnItem> asyncQueryOneTxnDetails(String nextQuery) {
        return this.onQueryOneTxnDetails(nextQuery)
                .exceptionally(ex -> {
                    throw this.handleAsyncException(ex);
                });
    }

    public CompletableFuture<QueryTxnItem> asyncQueryOneTxnDetails(UqdOptions options) {
        return this.onQueryOneTxnDetails(options).exceptionally(ex -> {
            throw this.handleAsyncException(ex);
        });
    }


    public CompletableFuture<QueryTxnItem> onQueryOneTxnDetails(UqdOptions options) {
        throw new UpckException("runQueryOneTransactionDetails options Not Support", UpckErrCode.DEV_NOT_SUPPORT);
    }

    public CompletableFuture<QueryTxnItem> onQueryOneTxnDetails(String nextQuery) {
        throw new UpckException("runQueryOneTransactionDetails nextQuery Not Support", UpckErrCode.DEV_NOT_SUPPORT);
    }

    public UpckException handleAsyncException(Throwable t) {
        if (t instanceof CompletionException || t instanceof ExecutionException) {
            t = Utils.or(t.getCause(), t);
        }
        this.getAppCtx().onHandleException(this.accountId, this.bankType(), t);
        return Utils.wrapException(t);
    }


    public UGB.ACCOUNT.UPI runQueryUpiDetails() {
        FICK.ACCOUNT.UPI fick = this.fetchUpiDetails();
        return FICK.convertRawToUGB(fick, UGB.ACCOUNT.UPI.class);
    }

    protected Object requestUpiDetails() {
        if (this.upiDetailResp == null) this.upiDetailResp = this.onRequestUpiDetails();
        return this.upiDetailResp;
    }

    private Object requestFetchCards() {
        if (this.optionsOfFetchCardsNeedKyc()) {
            UpckTokenStatus tokenStatus = this.checkAuthKycEnableDirect();
            if (tokenStatus == UpckTokenStatus.DISABLE) {
                throw UpckErrCode.ERR_KYC_REQUIRED_FETCH_CARD_LIST();
            }
        }
        if (this.cardResp == null) {
            this.cardResp = this.onRequestFetchCards();
        }
        return this.cardResp;
    }

    protected UpckTokenStatus checkAuthKycEnableDirect() {
        FICK.ACCOUNT.UPI<Object> upiDetails = this.fetchUpiDetails();
        return this.checkAuthKycEnable(upiDetails, null);
    }

    @Override
    public boolean checkLoginDataEnable() {
        FICK.AUTH.LOGIN fick;
        try {
            UGB.AUTH.LOGIN loginUGB = this.getAppCtx().onGetAuthLoginData(this.accountId);
            if (loginUGB == null) return false;
            fick = FICK.fromUGB(loginUGB, this.classLoginAuth());
        } catch (Exception e) {
            this.logConsoleE("onGetAuthLoginData Error: " + e.getMessage());
            return false;
        }
        if (fick == null) return false;
        if (fick.disable) return false;
        return this.onCheckLoginData(fick);
    }

    public boolean onCheckLoginData(FICK.AUTH.LOGIN fick) {
        return true;
    }


    protected boolean optionsOfFetchCardsNeedKyc() {
        return false;
    }


    public <T, D extends FICK.ACCOUNT.UPI<T>> D fetchUpiDetails() {
        if (this.upiDetailFickCache != null) {
            return (D) this.upiDetailFickCache;
        }

        /*
        isRefresh 的使用场景,如果有刷新的话,就回调给外面,增加实时更新频率
         */
        boolean isRefresh = this.upiDetailResp == null;
        Object upiDetailsRaw = this.requestUpiDetails();
        D fick = this.onConvertUpiDetails(upiDetailsRaw);

        if (isRefresh) {
            this.getAppCtx().onRefreshUpiDetails(
                    this.accountId, this.bankType(),
                    FICK.convertRawToUGB(fick, UGB.ACCOUNT.UPI.class));
        }
        this.upiDetailFickCache = (FICK.ACCOUNT.UPI<Object>) fick;
        return fick;
    }

    //
    public <T, F extends FICK.ACCOUNT.CARD<T>> Map<String, F> fetchCards() {
        if (this.cardsFickCache != null) {
            return (Map<String, F>) this.cardsFickCache;
        }
        /*
        isRefresh 的使用场景,如果有刷新的话,就回调给外面,增加实时更新频率
         */
        boolean isRefresh = this.cardResp == null;
        List<F> cardV2s = this.onConvertCardList(this.requestFetchCards());
        Map<String, F> retMap = new HashMap<>();
        if (!Utils.isEmpty(cardV2s)) {
            retMap = cardV2s.stream()
                    .collect(Collectors.toMap(c -> c.cardId, c -> c));
            if (isRefresh) {
                this.getAppCtx().onRefreshCards(
                        this.accountId, this.bankType(),
                        this.convertCardsU2F(retMap));
            }
        }
        this.cardsFickCache = (Map<String, FICK.ACCOUNT.CARD<Object>>) retMap;
        return retMap;
    }


    public <T, D extends FICK.ACCOUNT.CARD<T>> D fetchCardById(String cardId) {
        return (D) this.fetchCards().get(cardId);
    }


    public VpaDetails fetchVpaDetails() {
        return this.fetchVpaDetails(null);
    }

    public VpaDetails fetchVpaDetails(String selfVpa) {
        Map<String, VpaDetails> vpas = this.fetchUpiDetails().vpas;
        if (Utils.isEmpty(selfVpa)) {
            return vpas.values().stream()
                    .filter(v -> Utils.booleanValueOf(v.primary))
                    .findAny().orElse(null);
        }
        return vpas.get(selfVpa);
    }


    public Map<String, UGB.ACCOUNT.CARD> runCardQuery() {
        Map<String, FICK.ACCOUNT.CARD> fickMap = this.fetchCards();
        return this.convertCardsU2F(fickMap);
    }

    private <T, F extends FICK.ACCOUNT.CARD<T>> Map<String, UGB.ACCOUNT.CARD> convertCardsU2F(Map<String, F> fickMap) {
        return fickMap.values().stream()
                .collect(Collectors.toMap(
                        c -> c.cardId,
                        c -> FICK.convertRawToUGB(c, UGB.ACCOUNT.CARD.class)
                ));
    }

    private <T, F extends FICK.ACCOUNT.CARD<T>> Map<String, F> convertCardsF2U(Map<String, UGB.ACCOUNT.CARD> ugbCards) {
        return ugbCards.values().stream().collect(Collectors.toMap(c -> c.cardId,
                c -> FICK.convertRawToFICK(c, this.<T, F>classCard())
        ));
    }


    public VdtEchoData runEchoValidateAccount(Map<String, String> params) {
        ValidateManager validateManager = new ValidateManager(this);
        List<VDT> extraTasks = this.extraVdtTasks();
        if (!Utils.isEmpty(extraTasks)) validateManager.addTasks(extraTasks);
        return validateManager.startCheck();
    }


    public final Map<String, UGB.BANK> runQueryBankList() {
        List<FICK.BANK> fickList = this.onQueryBankList();
        return fickList.stream().collect(Collectors.toMap(bk -> bk.id,
                bk -> FICK.convertRawToUGB(bk, UGB.BANK.class)
        ));
    }

    @Override
    public final boolean runCardAdd(UGB.BANK bankInfo, String selfVpa) {
        FICK.BANK<Object> fickBank = FICK.convertRawToFICK(bankInfo, this.classBank());
        return this.onCardAdd(fickBank, selfVpa);
    }


    public IUpckDeviceUtils getDeviceUtils() {
        return deviceUtils;
    }

    public String getAccountId() {
        return this.accountId;
    }


    public UpckPayToTask transformPayTask(UpckPayRawTask rawPayTask) {
        UpckPayToTask payTo = new UpckPayToTask();
        PayUser payUser = rawPayTask.payUser;
        String payName = payUser.name;
        String nickName;
        if (StringUtils.isBlank(payName)) {
            nickName = Utils.randString(6);
        } else {
            nickName = payName.replaceAll("\\s+", "") + Utils.randString(6);
        }
//
//        if (payOptions.payMode == UpckPayMode.PayTransfer) {
//            payTo.id = rawPayTask.id;//存储最原始的
//        } else {
//            payTo.id = rawPayTask.smallCode;//存储最原始的
//        }

        payTo.id = rawPayTask.smallCode;//存储最原始的
        payTo.remarks = payTo.id;
        payTo.direction = this.parsePayDirection(rawPayTask);//针对印度的
        payTo.name = payName;
        payTo.nickName = nickName;
        payTo.amount = rawPayTask.amount;
        payTo.txnDate = System.currentTimeMillis();
        payTo.channel = rawPayTask.channel;
        payTo.payUser = payUser;
        if (payTo.channel == UpckPayChannel.BANK) {
            payTo.payUser.ifsc = payUser.ifsc.toUpperCase(Locale.ROOT);
            payTo.vpa = Utils.generateBankVpa(payUser.bankAccountNo, payUser.ifsc);
        }
        if (payTo.channel == UpckPayChannel.UPI_ID) {
            payTo.vpa = payUser.bankAccountNo;
        }
        if (payTo.channel == UpckPayChannel.QR_CODE || payTo.channel == UpckPayChannel.INTENT) {
            /**
             * 这里是转换二维码数据的核心
             */
            PayQR.parseSchemaToPayTo(payUser.scheme, payTo);
        }
        //--处理附加内容
//        payTo.amountUpi = payTo.amount.setScale(2).toString();
        payTo.rawTask = rawPayTask;
        return payTo;
    }

    @Override
    public boolean runVpaResetPrimary(String vpa) {
        return this.onVpaCreate(vpa, null);
    }

    @Override
    public boolean runVpaCreate(String vpa, String cardId) {
        this.runVpaCheckName(vpa);
        return this.onVpaCreate(vpa, cardId);
    }

    public List<QueryTxnItem> runQueryTransactions(UpckQueryOptions options) {
        return this.queryTransactions(options).join();
    }

    protected abstract CompletableFuture<List<QueryTxnItem>> onQueryTransactions(UpckQueryOptions options);


    private CompletableFuture<List<QueryTxnItem>> queryTransactions(UpckQueryOptions options) {
        return this.onQueryTransactions(options);


//        CompletableFuture<List<QueryTxnItem>> future = this.onQueryTransactions(options);
//        return future.thenApply(items -> {
//            if (Utils.isEmpty(items)) return items;
//            //输出解析错误
//            items.stream()
//                    .filter(it -> it.unexpectFormat)
//                    .forEach(it -> this.logConsoleW("Parse Txn Error:" + Utils.gson.toJson(it)));
//            return items;
//        });
    }

    public CompletableFuture<List<QueryTxnItem>> asyncQueryTransactions(UpckQueryOptions options) {
        return this.queryTransactions(options).exceptionally(ex -> {
            throw this.handleAsyncException(ex);
        });
    }


    @Override
    public boolean runVpaDelete(String vpa) {
        return false;
    }

    protected Direction parsePayDirection(UpckPayRawTask rawPayTask) {
        if (rawPayTask.countryId == UpiCountryType.IN) {
            String ifsc = rawPayTask.payUser.ifsc;
            if (StringUtils.isBlank(ifsc)) {
                return Direction.Other;
            }
            String ifscHeader = ifsc.trim().substring(0, 4);
            if (ifscHeader.equalsIgnoreCase(this.getIfscKeyCode())) {
                return Direction.Local;
            }
        }
        return Direction.Other;
    }

    public String getIfscKeyCode() {
        return "";
    }

    public void delay(long ts) {
        try {
            Thread.sleep(ts);
        } catch (Exception e) {
            throw new UpckException(e);
        }
    }


    public String formatTime(long ts, String format) {
        return Utils.formatTime(ts, format, this.getTimezone());
    }

    public String getTimezone() {
        UpiCountryType countryId = this.countryId();
        String timezone = "GMT+8:00";
        if (countryId == UpiCountryType.IN) {
            timezone = "GMT+5:30";
        } else if (countryId == UpiCountryType.BR) {
            timezone = "GMT-3:00";
        }
        return timezone;
    }

    @Override
    public final boolean runCardDelete(String cardId, String selfVpa) {
        FICK.ACCOUNT.CARD card = this.fetchCardById(cardId);
        if (card == null) throw UpckErrCode.ERR_CARD_NOT_FOUND();
        return this.onCardDelete(card, selfVpa);
    }

    public boolean onCardChangePin(FICK.ACCOUNT.CARD card, INPUT.PIN.CHANGE input) {
        return false;
    }

    public final boolean runCardChangePin(INPUT.PIN.CHANGE input) {
        if (Utils.isEmpty(input.newPin)) {
            throw new UpckException("New Pin Empty", DEV_FATAL);
        }
        if (Utils.isEmpty(input.oldPin)) {
            throw new UpckException("Old Pin Empty", DEV_FATAL);
        }
        if (Utils.isEmpty(input.cardId)) {
            throw new UpckException("CardId Empty", DEV_FATAL);
        }
        FICK.ACCOUNT.CARD card = this.fetchCardById(input.cardId);
        if (card == null) throw UpckErrCode.ERR_CARD_NOT_FOUND();
        UpckCredAllow credAllow = (UpckCredAllow) card.credAlloweds.get(UpckCredAllow.SubType.MPIN);
        int requireLen = credAllow.dLen;
        int newPinLen = input.newPin.length();
        int oldPinLen = input.oldPin.length();
        if (newPinLen != requireLen)
            throw UpckErrCode.ERR_CARD_PIN_LEN_WRONG(newPinLen, requireLen);

        if (oldPinLen != requireLen)

            throw UpckErrCode.ERR_CARD_PIN_LEN_WRONG(oldPinLen, requireLen);


        return this.onCardChangePin(card, input);
    }


    public final UGB.SESSION.PIN runCardSetPinPrepare(INPUT.PIN.SET.PREPARE input) {
        if (Utils.isEmpty(input.cardId)) throw new UpckException("SetPin CardId Empty", DEV_FATAL);
        if (Utils.isEmpty(input.selfVpa)) throw new UpckException("SetPin SelfVpa Empty", DEV_FATAL);
        Map<String, UGB.ACCOUNT.CARD> cards = this.runCardQuery();

        FICK.ACCOUNT.CARD fickCard = this.fetchCardById(input.cardId);
        VpaDetails vpaDetails = this.fetchVpaDetails(input.selfVpa);
        FICK.SESSION.PIN fick = this.onCardSetPinPrepare(fickCard, vpaDetails, input);
        fick.vpaDetails = vpaDetails;
        UGB.SESSION.PIN ugb = new UGB.SESSION.PIN(fick);
        ugb.card = FICK.convertRawToUGB(fickCard, UGB.ACCOUNT.CARD.class);
        return ugb;
    }

    public final boolean runCardSetPinConfirm(INPUT.PIN.SET.CONFIRM input, UGB.SESSION.PIN session) {
        if (Utils.isEmpty(input.otpCode))
            throw new UpckException("SetPin Confirm otpCode Emtpy", DEV_FATAL);
        if (Utils.isEmpty(input.atmPin)) throw new UpckException("SetPin Confirm atmPin Emtpy", DEV_FATAL);
        if (Utils.isEmpty(input.newSetMPin))
            throw new UpckException("SetPin Confirm newSetMPin Emtpy", DEV_FATAL);
        if (Utils.isEmpty(input.cardTailNumber))
            throw new UpckException("SetPin Confirm cardTailNumber Emtpy", DEV_FATAL);
        if (Utils.isEmpty(input.cardExpiredDate))
            throw new UpckException("SetPin Confirm cardExpireDate Emtpy", DEV_FATAL);
        return this.onCardSetPinConfirm(input, FICK.fromUGB(session, this.classPinSession()));
    }


    public <T> T runFunction(UpckFunctionType functionType, String... args) {
        throw new UpckException("Unsupported runFunction", DEV_FATAL);
    }


    public long parseTime(String input, String format) {
        return this.parseTime(input, format, Locale.getDefault(Locale.Category.FORMAT));
    }

    public long parseTime(String input, String format, Locale locale) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format, locale);
            sdf.setTimeZone(TimeZone.getTimeZone(this.getTimezone()));
            Date date = sdf.parse(input);
            return date.getTime();
        } catch (Exception e) {
            throw new UpckException(e);
        }
    }

    public void logV(String msg) {
        this.loggerDefault.logV(msg);
    }

    public void logD(String msg) {
        this.loggerDefault.logD(msg);
    }

    public void logI(String msg) {
        this.loggerDefault.logI(msg);
    }

    public void logW(String msg) {
        this.loggerDefault.logW(msg);
    }

    public void logE(String msg, Throwable t) {
        this.loggerDefault.logE(msg, t);
    }

    public void logE(String msg) {
        this.loggerDefault.logE(msg);
    }


    public void logF(String msg, Throwable t) {
        this.loggerDefault.logF(msg, t);

    }

    public void logF(String msg) {
        this.loggerDefault.logF(msg);
    }


    //--
    public void logRecordV(String msg) {
        this.loggerRecord.logV(msg);
    }

    public void logRecordD(String msg) {
        this.loggerRecord.logD(msg);
    }

    public void logRecordI(String msg) {
        this.loggerRecord.logI(msg);
    }

    public void logRecordW(String msg) {
        this.loggerRecord.logW(msg);
    }

    public void logRecordE(String msg, Throwable t) {
        this.loggerRecord.logE(msg, t);
    }

    public void logRecordE(String msg) {
        this.loggerRecord.logE(msg);
    }

    public void logRecordF(String msg, Throwable t) {
        this.loggerRecord.logF(msg, t);
    }

    public void logRecordF(String msg) {
        this.loggerRecord.logF(msg);
    }

    //--
    public void logConsoleV(String msg) {
        this.loggerConsole.logV(msg);
    }

    public void logConsoleD(String msg) {
        this.loggerConsole.logD(msg);
    }

    public void logConsoleI(String msg) {
        this.loggerConsole.logI(msg);
    }

    public void logConsoleW(String msg) {
        this.loggerConsole.logW(msg);
    }

    public void logConsoleE(String msg, Throwable t) {
        this.loggerConsole.logE(msg, t);
    }

    public void logConsoleE(String msg) {
        this.loggerConsole.logE(msg);
    }

    public void logConsoleF(String msg, Throwable t) {
        this.loggerConsole.logF(msg, t);
    }

    public void logConsoleF(String msg) {
        this.loggerConsole.logF(msg);
    }

    public boolean isRunning() {
        return true;
    }

    public UpiCountryType countryId() {
        return UpiCountryType.IN;
    }


    public QrValidate validateQr(PayQR payQR) {
        if (payQR == null) return new QrValidate("QrNull", UpckErrCode.PAY_QR_NULL);
        if (Utils.isEmpty(payQR.vpa)) return new QrValidate("Qr Vpa(pa) Emtpy", UpckErrCode.PAY_QR_VPA_EMPTY);
        if (payQR.amount == null) return new QrValidate("Qr Amount(am) Emtpy", UpckErrCode.PAY_QR_AMOUNT_EMPTY);
        if (Utils.isEmpty(payQR.currency))
            return new QrValidate("Qr Currency(cu) Emtpy", UpckErrCode.PAY_QR_CURRENCY_EMPTY);

        if (Utils.isEmpty(payQR.mcc)) {
            return new QrValidate("Qr Mcc(mc) Emtpy", UpckErrCode.PAY_QR_MCC_EMPTY);
        }

        if (Utils.isEmpty(payQR.merchantTransactionId) && Utils.isEmpty(payQR.upiTransactionId)) {
            return new QrValidate("Qr TxnRefId(tr) or Tid(tid) Emtpy", UpckErrCode.PAY_QR_TID_EMPTY);
        }
        if (Utils.isEmpty(payQR.mode)) this.logW("Qr Mode Emtpy");
        if (Utils.isEmpty(payQR.payeeName)) this.logW("Qr PayeeName(pn) Emtpy");
        if (Utils.isEmpty(payQR.txnNode)) this.logW("Qr TxnNode(tn) Emtpy");
        if (Utils.isEmpty(payQR.orgId)) this.logW("Qr orgid Emtpy");
        if (Utils.isEmpty(payQR.sign)) this.logW("Qr sign Emtpy");
        return null;
    }

    @Override
    public IpInfo testIpInfo() {
        FpiAccount fpiAccount = this.getFpiAccount();
        UpbBankerNet net = Utils.getRandomElement(this.listOfChannels());
        UPN.Options options = new UPN.Options()
                .method(UPN.Method.GET)
                .respType(IpInfo.class)
                .url("https://ipinfo.io/json");
        assert net != null;
        UPN.UpnResponse<IpInfo> resp = net.requestAsFetch(options);
        return Utils.gson.fromJson(resp.rawBody, IpInfo.class);
    }

    public <T> UpckException blankException(UPN.UpnResponse<T> resp) {
        return new UpckException(String.format("[%d]:Empty:%s", resp.code, resp.message), UpckErrCode.NET_BODY_EMPTY);
    }

    public <T> UpckException jsonCastException(UPN.UpnResponse<T> resp) {
        return new UpckException(String.format("code=[%d]Unexpect Json Format:%s", resp.code, resp.rawBody), UpckErrCode.UNKNOWN);
    }

}
