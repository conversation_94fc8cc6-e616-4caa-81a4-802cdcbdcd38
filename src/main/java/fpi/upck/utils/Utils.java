package fpi.upck.utils;

import com.google.common.collect.Iterables;
import com.google.gson.*;
import fpi.upck.common.entity.exceptions.UpckErrCode;
import fpi.upck.common.entity.exceptions.UpckException;
import okhttp3.RequestBody;
import okio.Buffer;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.*;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;


public class Utils {
    public static Gson gson = new Gson();
    private static Gson formatGson = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    private final static String HEX_CHARS = "0123456789abcdef";
    private final static char[] HEX_CHAR_ARRAY = HEX_CHARS.toCharArray();
    private final static String ASCII_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private final static String ASCII_CHARS_NO_NUM = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private final static String NUM_CHARS = "0123456789";


    public interface IForeachCaller<V> {
        boolean call(V value);
    }

    //单词转缩写
    public static String getAbbreviation(String input) {
        String[] words = input.split("\\s+"); // 按空格分割
        StringBuilder abbreviation = new StringBuilder();
        for (String word : words) {
            if (!word.isEmpty()) {
                abbreviation.append(word.charAt(0)); // 获取首字母
            }
        }
        return abbreviation.toString().toUpperCase();
    }

    public static <T> T getRandomElement(Collection<T> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }
        int size = collection.size();
        int index = new Random().nextInt(size);
        return Iterables.get(collection, index);
    }

    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    public static <K, T> T getRandomMapElement(Map<K, T> map) {
        K key = Utils.getRandomElement(map.keySet());
        return map.get(key);
    }


    public static <K, V> void foreachMap(Map<K, V> map, IForeachCaller<V> caller) {
        Set<K> keys = map.keySet();
        for (K key : keys) {
            if (caller.call(map.get(key))) {
                break;
            }
        }
    }


    public static BigDecimal parseToBigDecimal(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }
        return BigDecimal.valueOf(Double.parseDouble(input));
    }

    public static String randRange(int len, String range) {
        Random rnd = new Random();
        int rangeSize = range.length();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < len; i++) {
            int c = rnd.nextInt(rangeSize);
            sb.append(range.charAt(c));
        }
        return sb.toString();
    }

    public static List<Integer> vectors(int len) {
        ArrayList<Integer> ret = new ArrayList<>();
        for (int i = 0; i < len; i++) ret.add(i);
        return ret;
    }


    public static String randString(int length) {
        return Utils.randRange(length, ASCII_CHARS);
    }


    public static String randHexBytes(int length) {
        return Utils.randRange(length * 2, HEX_CHARS);
    }


    public static String md5(byte[] inputBytes) {
        byte[] secretBytes = null;
        try {
            secretBytes = MessageDigest.getInstance("md5").digest(inputBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new UpckException(e);
        }
        return Utils.bytesToHex(secretBytes);
    }

    public static String md5(String plainText)   {
        return Utils.md5(plainText.getBytes(StandardCharsets.UTF_8));
    }

    public static String aesEncrypt(String input, String key) {
        try {
            SecretKeySpec secKey = new SecretKeySpec(Utils.md5(key.getBytes(StandardCharsets.UTF_8))
                    .getBytes(StandardCharsets.UTF_8),
                    "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secKey);
            return Utils.bytesToHex(cipher.doFinal(input.getBytes(StandardCharsets.UTF_8))).toUpperCase(Locale.ROOT);
        } catch (Exception e) {
            throw new UpckException(e);
        }

    }

    public static String aesDecrypt(String input, String key) throws Exception {
        SecretKeySpec secKey = new SecretKeySpec(Utils.md5(key.getBytes(StandardCharsets.UTF_8))
                .getBytes(StandardCharsets.UTF_8),
                "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.DECRYPT_MODE, secKey);
        return new String(cipher.doFinal(Utils.hexToBytes(input)));
    }

    public static String randChars(int length) {
        return Utils.randRange(length, ASCII_CHARS_NO_NUM);
    }


    public static String randNums(int length) {
        return Utils.randRange(length, NUM_CHARS);
    }


    public static byte[] hexToBytes(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];

        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static String hexToJStr(String s) {
        return new String(hexToBytes(s));
    }

    public static byte[] convertToASCII(String string) {
        char[] ch = string.toCharArray();
        byte[] tmp = new byte[ch.length];
        for (int i = 0; i < ch.length; i++) {
            tmp[i] = (byte) Integer.valueOf(ch[i]).intValue();
        }
        return tmp;
    }

    public static byte[] joinBytes(byte[]... byteArrays) {
        if (ArrayUtils.isEmpty(byteArrays)) {
            return new byte[0];
        }
        int size = 0;
        for (byte[] bytes : byteArrays) {
            size += bytes.length;
        }
        ByteBuffer byteBuffer = ByteBuffer.allocate(size);
        for (byte[] bytes : byteArrays) {
            byteBuffer.put(bytes);
        }
        return byteBuffer.array();
    }

    public static String base64Encode(byte[] input) {
        return Base64.getEncoder().encodeToString(input);
    }

    public static byte[] base64EncodeToBytes(byte[] input) {
        return Base64.getEncoder().encode(input);
    }

    public static String base64Encode(String input) {
        return base64Encode(input.getBytes(StandardCharsets.UTF_8));
    }

    public static String base64UrlEncode(byte[] input) {
        return Base64.getUrlEncoder().encodeToString(input);
    }


    public static String urlEncode(String input) {
        return Utils.urlEncode(input, "UTF-8");

    }

    public static String urlEncode(String input, String charset) {
        if (Utils.isEmpty(charset)) charset = "UTF-8";
        try {
            return URLEncoder.encode(input, charset);
        } catch (UnsupportedEncodingException e) {
            throw new UpckException(e);
        }
    }

    public static String urlDecode(String input) {
        return Utils.urlDecode(input, "UTF-8");
    }

    public static String urlDecode(String input, String charset) {
        if (Utils.isEmpty(charset)) charset = "UTF-8";
        try {
            return URLDecoder.decode(input, charset);
        } catch (UnsupportedEncodingException e) {
            throw new UpckException(e);
        }
    }

    public static byte[] base64Decode(String input) {
        return Base64.getDecoder().decode(input);
    }

    public static byte[] base64UrlDecode(String input) {
        return Base64.getUrlDecoder().decode(input);
    }

    public static byte[] base64Decode(byte[] input) {
        return Base64.getDecoder().decode(input);
    }

    public static byte[] base64DecodeByApache(byte[] input) {
        return new org.apache.commons.codec.binary.Base64().decode(input);
    }

    public static String padZeros(String input, int maxLen) {
        return Utils.padZeros(input, maxLen, '0', true);
    }

    public static String padZeros(String input, int maxLen, char delimiter, boolean left) {
        //StringBuilder ret = new StringBuilder(input);
        if (StringUtils.isBlank(input)) {
            input = "";
        }
        int padLen = maxLen - input.length();
        if (padLen <= 0) {
            return input;
        }
        StringBuilder pad = new StringBuilder();
        for (int i = 0; i < padLen; i++) {
            pad.append(delimiter);
        }
        if (left) {
            return pad.toString() + input;
        } else {
            return input + pad.toString();
        }
    }

    public static String formatAmount(String amount, int scale) {
        return BigDecimal.valueOf(Double.parseDouble(amount)).setScale(scale).toString();
    }

    public static String formatAmount(String amount) {
        return Utils.formatAmount(amount, 2);
    }

    public static byte[] mergeBytes(byte[]... byteArrays) {
        ByteArrayOutputStream os = new ByteArrayOutputStream(1024);
        for (byte[] bytes : byteArrays) {
            try {
                if (bytes == null) {
                    continue;
                }
                os.write(bytes);
            } catch (IOException e) {
            }
        }
        return os.toByteArray();
    }

    public static byte[] subBytes(byte[] input, int start, int count) {
        byte[] output = new byte[count];
        System.arraycopy(input, start, output, 0, count);
        return output;
    }

    public static byte[] subBytes(byte[] input, int start) {
        int count = input.length - start;
        return subBytes(input, start, count);
    }

    public static String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int j = 0; j < bytes.length; j++) {
            int v = bytes[j] & 0xFF;
            hexChars[j * 2] = HEX_CHAR_ARRAY[v >>> 4];
            hexChars[j * 2 + 1] = HEX_CHAR_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String requestBodyToString(RequestBody requestBody) throws IOException {
        if (requestBody == null) return null;
        Buffer buffer = new Buffer();
        requestBody.writeTo(buffer);
        return buffer.readString(StandardCharsets.UTF_8);
    }

    public static JsonObject toJsonObject(Object input) {
        if (input == null) return null;
        String json = Utils.gson.toJson(input);
        return Utils.gson.fromJson(json, JsonObject.class);
    }


    /**
     * 合并两个json串，只增不减
     *
     * @param object1
     * @param object2
     * @return
     */
    public static <T> T merge(T object1, T object2) {
        if (object1 == null && object2 == null) return null;
        try {
            if (object1 == null) return object2;
            if (object2 == null) return object1;
            JsonObject jsonObject1 = new JsonParser().parse(Utils.gson.toJson(object1)).getAsJsonObject();
            JsonObject jsonObject2 = new JsonParser().parse(Utils.gson.toJson(object2)).getAsJsonObject();

            Set<String> keys = jsonObject2.keySet();
            for (String key : keys) {
                JsonElement value2 = jsonObject2.get(key);
                JsonElement value1 = jsonObject1.get(key);

                if (value1 != null) {
                    //Object value1 = jsonObject1.get(key);
                    if (!(value1 instanceof JsonObject)) {
                        jsonObject1.add(key, value2);
                        //jsonObject1.put(key, value2);
                    } else {
                        jsonObject1.add(key, merge(value1, value2));
                        //jsonObject1.put(key, mergeJsonObj(value1, value2));
                    }
                } else {
                    jsonObject1.add(key, value2);
                }
            }
            return (T) Utils.gson.fromJson(jsonObject1, object1.getClass());
            //return (T) JSON.toJavaObject(jsonObject1, object1.getClass());
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static BigDecimal jsonPathAsBigDecimal(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == JsonNull.INSTANCE) {
            return null;
        }
        return jsonElement.getAsBigDecimal();
    }

    public static JsonElement jsonPathAsElement(JsonElement json, String path) {
        return getJsonElement(json, path);
    }

    public static String jsonPathAsString(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsString();
    }

    public static Long jsonPathAsLong(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsLong();
    }

    public static Integer jsonPathAsInt(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsInt();
    }

    public static Boolean jsonPathAsBoolean(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsBoolean();
    }

    public static JsonObject jsonPathDelete(JsonObject json, String... paths) {
        if (Utils.isEmpty(paths)) return json;
        for (String p : paths) {
            Utils.jsonPathDeleteOne(json, p);
        }
        return json;
    }

    public static JsonObject jsonPathDeleteOne(JsonObject json, String path) {
        String[] parts = path.split("\\.|\\[|\\]");
        if (Utils.isEmpty(parts)) throw new UpckException("jsonPathDelete path len empty", UpckErrCode.UNKNOWN);
        String[] parents = ArrayUtils.subarray(parts, 0, parts.length - 1);
        String pathParent = String.join(".", parents);
        String childNode = parts[parts.length - 1];
        JsonObject parentObj = Utils.jsonPathAsJsonObject(json, pathParent);
        parentObj.remove(childNode);
        return json;
    }


    public static <T> T jsonPathDeleteT(T input, String... paths) {
        if (input == null) return null;
        JsonObject prevObj = Utils.toJsonObject(input);
        JsonObject newObj = Utils.jsonPathDelete(prevObj, paths);
        return Utils.gson.fromJson(newObj, (Type) input.getClass());
    }

    public static String jsonPathDeleteForString(String json, String path) {
        JsonObject jsonObject = Utils.gson.fromJson(json, JsonObject.class);
        JsonObject newJson = Utils.jsonPathDelete(jsonObject, path);
        return Utils.gson.toJson(newJson);
    }

    public static JsonArray jsonPathAsArray(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsJsonArray();
    }

    public static JsonElement getJsonElement(JsonElement json, String path) {
        String[] parts = path.split("\\.|\\[|\\]");
        JsonElement result = json;
        for (String key : parts) {
            key = key.trim();
            if (key.isEmpty())
                continue;
            if (result == null || result.isJsonNull()
                    || result.isJsonPrimitive() //如果是基础类型,又怎么会有子字段呢?
            ) {
                return null;
            }
            if (result.isJsonObject()) {
                result = ((JsonObject) result).get(key);
            } else if (result.isJsonArray()) {
                result = ((JsonArray) result).get(Integer.valueOf(key));
            } else {
                break;
            }
        }
        if (result != null && result.isJsonNull()) {
            return null;
        }

        return result;
    }

    public static JsonObject jsonPathAsJsonObject(JsonElement json, String path) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return jsonElement.getAsJsonObject();
    }

    public static <T> T jsonPathAsClass(JsonElement json, String path, Class<T> clazz) {
        return jsonPathAsType(json, path, clazz);
    }

    public static <T> T jsonPathAsType(JsonElement json, String path, Type type) {
        JsonElement jsonElement = getJsonElement(json, path);
        if (jsonElement == null) {
            return null;
        }
        return Utils.gson.fromJson(jsonElement, type);
    }


    public static byte[] sha256(byte[] input) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(input);
            return messageDigest.digest();
        } catch (Exception v3) {
            return null;
        }
    }


    public static String sha1(String input) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(input.getBytes());
            byte[] outBytes = messageDigest.digest();
            return Utils.bytesToHex(outBytes);
        } catch (Exception v3) {
            return null;
        }
    }

    public static byte[] sha1(byte[] input) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-1");
            messageDigest.update(input);
            return messageDigest.digest();
        } catch (Exception v3) {
            return null;
        }
    }

    public static byte[] hmac_sha1(byte[] keyBytes, byte[] data) {
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec signingKey = new SecretKeySpec(keyBytes, "HmacSHA1");
            mac.init(signingKey);
            return mac.doFinal(data);
        } catch (Exception e) {
            throw new UpckException(e);
        }
    }


    public static List<byte[]> byteSplit(byte[] input, byte delimiter) {
        List<byte[]> ret = new ArrayList<>();
        int lastIndex = 0;
        for (int i = 0; i < input.length; i++) {
            if (input[i] == delimiter) {
                int len = i - lastIndex;
                byte[] splitBytes = new byte[len];
                System.arraycopy(input, lastIndex, splitBytes, 0, len);
                ret.add(splitBytes);
                lastIndex = i + 1;
            }
        }
        int len = input.length - lastIndex;
        byte[] splitBytes = new byte[len];
        System.arraycopy(input, lastIndex, splitBytes, 0, len);
        ret.add(splitBytes);
        return ret;
    }

    public static <T> T copy(T input) {
        if (input == null) return null;
        return Utils.gson.fromJson(Utils.gson.toJson(input), (Type) input.getClass());
    }

    public static <R> R copy(Object input, Class<R> clazz) {
        if (input == null) return null;
        return Utils.gson.fromJson(Utils.gson.toJson(input), clazz);
    }


    public static List<byte[]> byteSplit2(byte[] input, byte delimiter) {

        List<byte[]> ret = new ArrayList<>();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        int i = 0;
        while (true) {
            if (i > input.length) {
                if (bos.size() > 0) {
                    ret.add(bos.toByteArray());
                }
                return ret;
            }
            int iByte = input[i];
            int v6 = iByte == delimiter ? 0 : 1;
            if (v6 != 0) {
                bos.write(iByte);
            } else {
                iByte = iByte % 2 != 0 ? 0 : 1;
                if (iByte != 1) {
                    break;
                }
                ret.add(bos.toByteArray());
                bos.reset();
            }
            ++i;
        }
        ret.add(bos.toByteArray());
        bos.reset();
        return ret;
    }


    public static String sha256(String input) {
        return Utils.bytesToHex(Objects.requireNonNull(sha256(input.getBytes())));
    }

    public static <T> T cloneByJson(T object) {
        if (object == null)
            return null;
        String json = Utils.gson.toJson(object);
        return Utils.gson.fromJson(json, (Type) object.getClass());
    }

    //    public static <T> T mapToObject(Map<String, Object> mapObj, Class<T> clazz) {
//        if (mapObj == null) {
//            return null;
//        }
//        String json = gson.toJson(mapObj);
//        return gson.fromJson(json, clazz);
//    }
    public static <T> T mapToObject(Map<String, Object> mapObj, Type type) {
        if (mapObj == null) {
            return null;
        }
        String json = gson.toJson(mapObj);
        return gson.fromJson(json, type);
    }

    public static Map<String, String> urlParamsToMap(String query) {
        return Utils.urlParamsToMap(query, true);
    }

    public static Map<String, String> urlParamsToMap(String query, boolean decode) {
        Map<String, String> map = new HashMap<>();
        if (Utils.isEmpty(query)) return map;
        String[] kvs = query.trim().split("&");
        if (Utils.isEmpty(kvs)) return map;
        for (String kv : kvs) {
            String[] splitKv = kv.split("=");
            String key = splitKv[0];
            if (splitKv.length < 2) {
                map.put(key, "");
                continue;
            }
            String value = splitKv[1];
            if (decode && !Utils.isEmpty(value)) {
                value = Utils.urlDecode(value);
            }
            map.put(key, value);
        }
        return map;
    }

    public static String mapToUrlParams(Map<String, String> mapObj) throws UnsupportedEncodingException {
        return Utils.mapToUrlParams(mapObj, false);
    }

    public static String mapToUrlParams(Map<String, String> mapObj, boolean encode) {
        Set<String> keys = mapObj.keySet();
        List<String> params = new ArrayList<>();
        for (String key : keys) {
            String value = "" + mapObj.get(key);

            if (encode) value = Utils.urlEncode(value);

            params.add(key + "=" + value);
        }
        return String.join("&", params);
    }


    public static String formatBalance(BigDecimal balance) {
        if (balance == null)
            return null;
        return String.format("%.2f", balance);
    }

    public static String toKv(String key, String value) {
        return String.format("%s=%s", key, value);
    }

    public static String safe(String input) {
        return input == null ? "" : input;
    }


    public static String formBodyToUtf(RequestBody requestBody) {
        if (requestBody == null) return "";

        try {
            Buffer buffer = new Buffer();
            requestBody.writeTo(buffer);
            return buffer.readUtf8();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Map<String, String> objectToNestedMap(Object input) throws UpckException {
        return objectToNestedMap(input, "", ".");
    }

    public static Map<String, String> objectToNestedMap(Object obj, String prefixKey) throws UpckException {
        return objectToNestedMap(obj, prefixKey, ".");
    }

    public static Map<String, String> objectToNestedMap(Object obj, String prefixKey, String delimiter) throws UpckException {
        JsonElement jsonElement = Utils.gson.toJsonTree(obj);
        Map<String, String> nestedMap = new HashMap<>();
        objectToNestedMapTraverse(nestedMap, jsonElement, prefixKey, delimiter);
        return nestedMap;
    }


    public static void objectToNestedMapTraverse(Map<String, String> nestedMap, JsonElement element, String parentKey, String delimiter) throws UpckException {
        if (element.isJsonPrimitive()) {
            JsonPrimitive primitive = element.getAsJsonPrimitive();
            if (primitive != null && primitive.isString()) {
                nestedMap.put(parentKey, primitive.getAsString());
            } else {
                nestedMap.put(parentKey, "" + primitive);
            }
        } else if (element.isJsonArray()) {
            //这里临时 不支持数组
            throw new UpckException("Nest Map Not Supported Array", UpckErrCode.DEV_FATAL);
        } else if (element.isJsonNull()) {
            throw new UpckException("Nest Map Not Supported Null", UpckErrCode.DEV_FATAL);
        } else {
            JsonObject obj = element.getAsJsonObject();
            for (Map.Entry<String, JsonElement> entry : obj.entrySet()) {
                String key = entry.getKey();
                if (!StringUtils.isBlank(parentKey)) {
                    key = parentKey + delimiter + key;
                }
                objectToNestedMapTraverse(nestedMap, entry.getValue(), key, delimiter);
            }
        }
    }


//    public static <T> T parseDynamicParams(Map<String, String> params, Class<T> clazz, boolean enableDecode) {
//        return parseDynamicParams(params, clazz, enableDecode, clazz.getSimpleName());
//    }
//
//    /**
//     * 这里的处理效率感觉很低,以后还要优化一下
//     *
//     * @param <T>
//     * @param params
//     * @param clazz
//     * @param enableDecode
//     * @param prefixList
//     * @return
//     * @throws Exception
//     */
//    public static <T> T parseDynamicParams(Map<String, String> params,
//                                           Class<T> clazz,
//                                           boolean enableDecode, String... prefixList) {
//        if (params == null || params.size() == 0) {
//            return null;
//        }
//        if (ArrayUtils.isEmpty(prefixList)) {
//            return null;
//        }
//        Properties properties = new Properties();
//        T initialObj;
//        try {
//            initialObj = clazz.newInstance();
//            String prefixTag = String.join("#", prefixList) + "#";
//            Set<Map.Entry<String, String>> entrySet = params.entrySet();
//            for (Map.Entry<String, String> entry : entrySet) {
//                String key = entry.getKey();
//                if (!key.startsWith(prefixTag)) {
//                    continue;
//                }
//                String value = params.get(key);
//                String newKey = key.replace(prefixTag, "");
//                newKey = newKey.replace("#", ".");
//                if (enableDecode) {
//                    value = URLDecoder.decode(value, "UTF-8");
//
//                }
//                properties.setProperty(newKey, value);
//            }
//        } catch (Exception e) {
//            throw new UpckException(e);
//        }
//
//        if (properties.size() == 0) {
//            return null;
//        }
//
//        try {
//            JavaPropsMapper mapper = new JavaPropsMapper();
//            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
//            T serverConfig = mapper.readValue(properties, clazz);
//            T ret = Utils.merge(initialObj, serverConfig);
//            return ret;
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//
//        return null;
//    }

    public static String formatJson(Object input) {
        if (input == null) return null;
        return Utils.formatGson.toJson(input);
    }

    public static String formatTime(long ts, String format, String timezone) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        dateFormat.setTimeZone(TimeZone.getTimeZone(timezone));
        return dateFormat.format(ts);
    }

    public static boolean booleanValueOf(Boolean input) {
        return input != null && input;
    }

    public static boolean integerEq(Integer input, int other) {
        if (input == null) {
            return false;
        }
        return input == other;
    }

    public static String randomWifiMac() {
        String[] randWifiMacList = new String[6];
        for (int i = 0; i < randWifiMacList.length; i++) {
            randWifiMacList[i] = Utils.randHexBytes(1);
        }
        return String.join(":", randWifiMacList);
    }


//    public static String[] getNullPropertyNames(Object source) {
//        List<String> nullValuePropertyNames = new ArrayList<>();
//        for (Field f : source.getClass().getDeclaredFields()) {
//            try {
//                if (f.get(source) == null) {
//                    nullValuePropertyNames.add(f.getName());
//                }
//            } catch (IllegalAccessException e) {
//                e.printStackTrace();
//            }
//        }
//        return nullValuePropertyNames.toArray(new String[0]);
//    }


    public static boolean strEq(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return false;
        }
        return str1.equals(str2);
    }

    public static String mapToCookies(Map<String, String> cookieMap) {
        Set<String> keys = cookieMap.keySet();
        List<String> itemList = new ArrayList<>();
        for (String key : keys) {
            itemList.add(Utils.toKv(key, cookieMap.get(key)));
        }
        String[] itemArray = new String[itemList.size()];
        itemArray = itemList.toArray(itemArray);
        return String.join("; ", itemArray);
    }

    public static <T, D extends Collection<T>> boolean isEmpty(D collection) {
        return CollectionUtils.isEmpty(collection);
    }


    public static boolean isNull(Object... objects) {
        return Arrays.stream(objects)
                .anyMatch(Objects::isNull);
    }

    public static <T> boolean isEmpty(String input) {
        return StringUtils.isEmpty(input);
    }

    public static <T> boolean isEmpty(T[] arr) {
        return ArrayUtils.isEmpty(arr);
    }


    public static <K, V, T extends Map<K, V>> boolean isEmpty(T map) {
        return map == null || map.isEmpty();
    }

    public static boolean isEmpty(JsonArray input) {
        return input == null || input.size() == 0;
    }

    public static boolean isEmpty(byte[] arr) {
        return ArrayUtils.isEmpty(arr);
    }

    public static String or(String... inputs) {
        for (String i : inputs) {
            if (Utils.isEmpty(i)) continue;
            return i;
        }
        return null;
    }

    public interface OrCall<T> {
        T todo();
    }

    public static <T> T or(T... inputs) {
        for (T i : inputs) {
            if (i == null) continue;
            return i;
        }
        return null;
    }

    public static <D, T extends Collection<D>> T or(Collection<D>... inputs) {
        for (Collection<D> i : inputs) {
            if (Utils.isEmpty(i)) continue;
            return (T) i;
        }
        return null;
    }


    @SafeVarargs
    public static <T, D extends OrCall<T>> T or(D... inputs) {
        for (OrCall<T> i : inputs) {
            try {
                T ret = i.todo();
                if (ret == null) continue;
                return ret;
            } catch (Exception e) {
            }
        }
        return null;
    }


    public static void mapPutIfExist(Map<String, String> input, String key, String value) {
        if (!StringUtils.isBlank(value)) {
            input.put(key, value);
        }
    }

    public static Map<String, String> parseHttpCookies(List<HttpCookie> httpCookies) {
        Map<String, String> cookieMap = new HashMap<>();
        return null;
    }

    public static String generateBankVpa(String accountNo, String ifsc) {
        return String.format("%s@%s.ifsc.npci", accountNo, ifsc);
    }

    public static BigDecimal toBig(String input) {
        if (input == null) return null;
        return new BigDecimal(input);
    }

    public static BigDecimal toBig(Integer input) {
        if (input == null) return null;
        return new BigDecimal(input);
    }

    public static BigDecimal toBig(Double input) {
        if (input == null) return null;
        return new BigDecimal(input);
    }

    public static String parsePspHandler(String vpa) {
        if (Utils.isEmpty(vpa)) return null;
        String psp = vpa.split("@")[1];
        if (Utils.isEmpty(psp)) return null;
        return psp.toLowerCase(Locale.ROOT).trim();
    }

    public static void sleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (Exception e) {
            throw new UpckException(e);
        }
    }

    public static long parseExpToMillis(String str) {
        if (str.endsWith("ms")) {
            return Long.parseLong(str.replace("ms", ""));
        } else if (str.endsWith("s")) {
            return Long.parseLong(str.replace("s", "")) * 1000;
        } else if (str.endsWith("m")) {
            return Long.parseLong(str.replace("m", "")) * 60 * 1000;
        } else if (str.endsWith("h")) {
            return Long.parseLong(str.replace("h", "")) * 60 * 60 * 1000;
        } else if (str.endsWith("d")) {
            return Long.parseLong(str.replace("d", "")) * 24 * 60 * 60 * 1000;
        }
        throw new IllegalArgumentException("Unsupported time format: " + str);
    }

    public static URL url(String input) {
        try {
            return new URL(input);
        } catch (MalformedURLException e) {
            throw new UpckException(e);
        }
    }

    public static UpckException wrapException(Throwable t) {
        if (t instanceof CompletionException || t instanceof ExecutionException) {
            t = Utils.or(t.getCause(), t);
        }
        if (t instanceof UpckException) {
            return (UpckException) t;
        }
        return new UpckException(t);
    }
}
